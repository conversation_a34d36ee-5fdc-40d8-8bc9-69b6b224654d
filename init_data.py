#!/usr/bin/env python3
"""
初始化示例数据脚本
运行此脚本来创建示例分类、产品、轮播图等数据
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Category, Product, Banner, Page, Review, Setting, NewsPost

def create_sample_data():
    """创建示例数据"""
    app = create_app()
    
    with app.app_context():
        # 清空现有数据（可选）
        print("🗑️  Clearing existing data...")
        db.drop_all()
        db.create_all()
        
        # 创建设置
        print("⚙️  Creating settings...")
        create_settings()
        
        # 创建分类
        print("📂 Creating categories...")
        create_categories()
        
        # 创建产品
        print("📦 Creating products...")
        create_products()
        
        # 创建轮播图
        print("🖼️  Creating banners...")
        create_banners()
        
        # 创建页面
        print("📄 Creating pages...")
        create_pages()
        
        # 创建评价
        print("⭐ Creating reviews...")
        create_reviews()
        
        # 创建新闻
        print("📰 Creating news posts...")
        create_news()
        
        db.session.commit()
        print("✅ Sample data created successfully!")

def create_settings():
    """创建网站设置"""
    settings = [
        {
            'key': 'site_title',
            'value_en': 'Pecco Pet Portal - Happy Pets Happy Owner',
            'value_zh': 'Pecco宠物门户 - 快乐宠物快乐主人',
            'description': '网站标题'
        },
        {
            'key': 'site_description',
            'value_en': 'Light luxury pet supplies for a pawsome life',
            'value_zh': '轻奢宠物用品，为美好生活而生',
            'description': '网站描述'
        },
        {
            'key': 'contact_email',
            'value_en': '<EMAIL>',
            'value_zh': '<EMAIL>',
            'description': '联系邮箱'
        },
        {
            'key': 'contact_phone',
            'value_en': '+****************',
            'value_zh': '+86 ************',
            'description': '联系电话'
        },
        {
            'key': 'brand_slogan',
            'value_en': 'Happy Pets Happy Owner',
            'value_zh': '快乐宠物快乐主人',
            'description': '品牌标语'
        }
    ]
    
    for setting_data in settings:
        setting = Setting(**setting_data)
        db.session.add(setting)

def create_categories():
    """创建宠物分类"""
    categories = [
        {
            'name_en': 'Dogs',
            'name_zh': '狗狗用品',
            'description_en': 'Premium products for your beloved dogs',
            'description_zh': '为您心爱的狗狗提供优质产品',
            'emoji': '🐶',
            'sort_order': 1
        },
        {
            'name_en': 'Cats',
            'name_zh': '猫咪用品',
            'description_en': 'Luxury items for your precious cats',
            'description_zh': '为您珍贵的猫咪提供奢华用品',
            'emoji': '🐱',
            'sort_order': 2
        },
        {
            'name_en': 'Birds',
            'name_zh': '鸟类用品',
            'description_en': 'Everything your feathered friends need',
            'description_zh': '您的羽毛朋友需要的一切',
            'emoji': '🐦',
            'sort_order': 3
        },
        {
            'name_en': 'Fish',
            'name_zh': '鱼类用品',
            'description_en': 'Aquatic supplies for healthy fish',
            'description_zh': '为健康鱼类提供水族用品',
            'emoji': '🐠',
            'sort_order': 4
        },
        {
            'name_en': 'Small Pets',
            'name_zh': '小宠用品',
            'description_en': 'Care products for rabbits, hamsters and more',
            'description_zh': '为兔子、仓鼠等小宠物提供护理产品',
            'emoji': '🐹',
            'sort_order': 5
        },
        {
            'name_en': 'Reptiles',
            'name_zh': '爬虫用品',
            'description_en': 'Specialized products for reptile care',
            'description_zh': '爬虫护理专用产品',
            'emoji': '🦎',
            'sort_order': 6
        }
    ]
    
    for cat_data in categories:
        category = Category(**cat_data)
        db.session.add(category)
    
    db.session.flush()  # 获取ID

def create_products():
    """创建示例产品"""
    # 获取分类
    dog_category = Category.query.filter_by(name_en='Dogs').first()
    cat_category = Category.query.filter_by(name_en='Cats').first()
    
    products = [
        {
            'title_en': 'Luxury Dog Bed with Memory Foam',
            'title_zh': '记忆海绵豪华狗床',
            'description_en': 'Premium orthopedic dog bed with memory foam for ultimate comfort. Features washable cover and non-slip bottom.',
            'description_zh': '采用记忆海绵的优质矫形狗床，提供极致舒适。配有可洗床套和防滑底部。',
            'short_description_en': 'Orthopedic memory foam bed for dogs',
            'short_description_zh': '狗狗矫形记忆海绵床',
            'price': 89.99,
            'compare_price': 129.99,
            'sku': 'DOG-BED-001',
            'vendor': 'Pecco Premium',
            'category_id': dog_category.id,
            'is_featured': True,
            'is_new': True,
            'sort_order': 1
        },
        {
            'title_en': 'Interactive Smart Dog Toy',
            'title_zh': '智能互动狗玩具',
            'description_en': 'AI-powered interactive toy that keeps your dog engaged and entertained. App-controlled with treat dispensing feature.',
            'description_zh': 'AI驱动的互动玩具，让您的狗狗保持参与和娱乐。应用程序控制，带有零食分发功能。',
            'short_description_en': 'AI-powered interactive dog toy',
            'short_description_zh': 'AI驱动的互动狗玩具',
            'price': 149.99,
            'sku': 'DOG-TOY-001',
            'vendor': 'Pecco Tech',
            'category_id': dog_category.id,
            'is_hot': True,
            'sort_order': 2
        },
        {
            'title_en': 'Premium Cat Scratching Tower',
            'title_zh': '高级猫抓板塔',
            'description_en': 'Multi-level scratching tower with sisal rope and plush platforms. Perfect for multiple cats.',
            'description_zh': '多层抓板塔，配有剑麻绳和毛绒平台。非常适合多只猫咪。',
            'short_description_en': 'Multi-level cat scratching tower',
            'short_description_zh': '多层猫抓板塔',
            'price': 199.99,
            'compare_price': 249.99,
            'sku': 'CAT-TOWER-001',
            'vendor': 'Pecco Premium',
            'category_id': cat_category.id,
            'is_featured': True,
            'sort_order': 3
        },
        {
            'title_en': 'Automatic Pet Feeder with Camera',
            'title_zh': '带摄像头的自动宠物喂食器',
            'description_en': 'Smart feeder with HD camera, portion control, and smartphone app. Never miss feeding time again.',
            'description_zh': '智能喂食器，配有高清摄像头、分量控制和智能手机应用。再也不会错过喂食时间。',
            'short_description_en': 'Smart feeder with HD camera',
            'short_description_zh': '带高清摄像头的智能喂食器',
            'price': 299.99,
            'sku': 'FEEDER-001',
            'vendor': 'Pecco Tech',
            'category_id': dog_category.id,
            'is_new': True,
            'is_hot': True,
            'sort_order': 4
        }
    ]
    
    for prod_data in products:
        product = Product(**prod_data)
        db.session.add(product)

def create_banners():
    """创建轮播图"""
    banners = [
        {
            'title_en': 'Welcome to Pecco Pet Portal',
            'title_zh': '欢迎来到Pecco宠物门户',
            'subtitle_en': 'Happy Pets, Happy Owner',
            'subtitle_zh': '快乐宠物，快乐主人',
            'description_en': 'Discover our premium collection of luxury pet supplies',
            'description_zh': '探索我们的高端奢华宠物用品系列',
            'image_desktop': 'banner1.jpg',
            'link_url': '/products',
            'button_text_en': 'Shop Now',
            'button_text_zh': '立即购买',
            'sort_order': 1
        },
        {
            'title_en': 'New Smart Pet Technology',
            'title_zh': '全新智能宠物科技',
            'subtitle_en': 'Innovation meets pet care',
            'subtitle_zh': '创新与宠物护理的完美结合',
            'description_en': 'Experience the future of pet care with our smart products',
            'description_zh': '通过我们的智能产品体验宠物护理的未来',
            'image_desktop': 'banner2.jpg',
            'link_url': '/products?category=smart',
            'button_text_en': 'Explore Tech',
            'button_text_zh': '探索科技',
            'sort_order': 2
        }
    ]
    
    for banner_data in banners:
        banner = Banner(**banner_data)
        db.session.add(banner)

def create_pages():
    """创建静态页面"""
    pages = [
        {
            'title_en': 'About Us',
            'title_zh': '关于我们',
            'slug': 'about-us',
            'content_en': '''
            <h2>Welcome to Pecco Pet Portal</h2>
            <p>At Pecco, we believe that every pet deserves the finest care and love. Our mission is to provide luxury pet supplies that enhance the bond between pets and their owners.</p>
            <p>Founded with a passion for pets and a commitment to quality, we carefully curate each product to ensure it meets our high standards of excellence.</p>
            ''',
            'content_zh': '''
            <h2>欢迎来到Pecco宠物门户</h2>
            <p>在Pecco，我们相信每只宠物都应该得到最好的关爱和呵护。我们的使命是提供奢华的宠物用品，增进宠物与主人之间的感情。</p>
            <p>我们怀着对宠物的热爱和对质量的承诺而成立，精心挑选每一件产品，确保其符合我们的高标准。</p>
            '''
        },
        {
            'title_en': 'Brand Story',
            'title_zh': '品牌故事',
            'slug': 'brand-story',
            'content_en': '''
            <h2>Our Story</h2>
            <p>Pecco was born from a simple belief: pets are family, and family deserves the best.</p>
            <p>Our journey began when our founder, a devoted pet parent, couldn't find products that matched the love and care they wanted to give their furry companions.</p>
            ''',
            'content_zh': '''
            <h2>我们的故事</h2>
            <p>Pecco诞生于一个简单的信念：宠物是家人，家人应该得到最好的。</p>
            <p>我们的旅程始于我们的创始人，一位忠诚的宠物家长，无法找到与他们想要给予毛茸茸伙伴的爱和关怀相匹配的产品。</p>
            '''
        }
    ]
    
    for page_data in pages:
        page = Page(**page_data)
        db.session.add(page)

def create_reviews():
    """创建客户评价"""
    reviews = [
        {
            'customer_name': 'Sarah Johnson',
            'rating': 5,
            'title_en': 'Amazing Quality!',
            'title_zh': '质量惊人！',
            'content_en': 'The luxury dog bed exceeded my expectations. My golden retriever loves it and sleeps so peacefully now.',
            'content_zh': '这款豪华狗床超出了我的期望。我的金毛猎犬很喜欢，现在睡得很安稳。',
            'is_featured': True
        },
        {
            'customer_name': 'Mike Chen',
            'rating': 5,
            'title_en': 'Smart and Convenient',
            'title_zh': '智能便捷',
            'content_en': 'The automatic feeder with camera is a game-changer. I can monitor and feed my cat even when I\'m at work.',
            'content_zh': '带摄像头的自动喂食器改变了游戏规则。即使在工作时，我也能监控和喂养我的猫。',
            'is_featured': True
        },
        {
            'customer_name': 'Emma Wilson',
            'rating': 4,
            'title_en': 'Great Products',
            'title_zh': '优质产品',
            'content_en': 'Love the quality and design of Pecco products. My pets are happier and healthier.',
            'content_zh': '喜欢Pecco产品的质量和设计。我的宠物更快乐更健康。',
            'is_featured': True
        }
    ]
    
    for review_data in reviews:
        review = Review(**review_data)
        db.session.add(review)

def create_news():
    """创建新闻文章"""
    news_posts = [
        {
            'title_en': '5 Tips for Choosing the Perfect Pet Bed',
            'title_zh': '选择完美宠物床的5个技巧',
            'excerpt_en': 'Learn how to select the ideal bed for your furry friend\'s comfort and health.',
            'excerpt_zh': '了解如何为您毛茸茸的朋友选择理想的床铺，确保舒适和健康。',
            'content_en': '''
            <p>Choosing the right bed for your pet is crucial for their comfort and health. Here are our top 5 tips...</p>
            <ol>
                <li>Consider your pet's size and sleeping habits</li>
                <li>Look for orthopedic support</li>
                <li>Choose washable materials</li>
                <li>Consider the room temperature</li>
                <li>Think about durability</li>
            </ol>
            ''',
            'content_zh': '''
            <p>为您的宠物选择合适的床铺对它们的舒适和健康至关重要。以下是我们的5个顶级建议...</p>
            <ol>
                <li>考虑您宠物的大小和睡眠习惯</li>
                <li>寻找矫形支撑</li>
                <li>选择可洗材料</li>
                <li>考虑房间温度</li>
                <li>考虑耐用性</li>
            </ol>
            ''',
            'is_published': True,
            'published_at': datetime.utcnow() - timedelta(days=2)
        },
        {
            'title_en': 'The Future of Smart Pet Technology',
            'title_zh': '智能宠物技术的未来',
            'excerpt_en': 'Explore how technology is revolutionizing pet care and what\'s coming next.',
            'excerpt_zh': '探索技术如何革命性地改变宠物护理以及接下来会发生什么。',
            'content_en': '''
            <p>Smart pet technology is rapidly evolving, bringing new possibilities for pet care...</p>
            <p>From AI-powered toys to health monitoring devices, the future looks bright for our furry friends.</p>
            ''',
            'content_zh': '''
            <p>智能宠物技术正在快速发展，为宠物护理带来新的可能性...</p>
            <p>从AI驱动的玩具到健康监测设备，我们毛茸茸朋友的未来看起来很光明。</p>
            ''',
            'is_published': True,
            'is_featured': True,
            'published_at': datetime.utcnow() - timedelta(days=5)
        }
    ]
    
    for news_data in news_posts:
        news_post = NewsPost(**news_data)
        db.session.add(news_post)

if __name__ == '__main__':
    create_sample_data()
