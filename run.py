#!/usr/bin/env python3
"""
Pecco Pet Portal - 启动脚本
轻奢宠物用品官网门户网站
"""

import os
import sys
from app import create_app, db
from flask_migrate import upgrade, init, migrate

def create_directories():
    """创建必要的目录"""
    directories = [
        'static/uploads',
        'static/uploads/products',
        'static/uploads/banners',
        'static/uploads/categories',
        'static/uploads/reviews',
        'static/uploads/news',
        'static/images',
        'logs'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def init_database():
    """初始化数据库"""
    try:
        # 检查是否已经初始化过迁移
        if not os.path.exists('migrations'):
            print("🔧 Initializing database migrations...")
            init()
        
        # 创建迁移
        print("🔧 Creating database migration...")
        migrate(message='Initial migration')
        
        # 应用迁移
        print("🔧 Applying database migrations...")
        upgrade()
        
        print("✅ Database initialized successfully!")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def setup_sample_data():
    """设置示例数据"""
    try:
        from init_data import create_sample_data
        print("📦 Creating sample data...")
        create_sample_data()
        print("✅ Sample data created successfully!")
        return True
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")
        return False

def main():
    """主函数"""
    print("🐾 Welcome to Pecco Pet Portal Setup!")
    print("=" * 50)
    
    # 创建应用实例
    app = create_app()
    
    with app.app_context():
        # 创建目录
        print("\n📁 Creating directories...")
        create_directories()
        
        # 初始化数据库
        print("\n🗄️  Setting up database...")
        if init_database():
            # 创建示例数据
            print("\n📊 Setting up sample data...")
            setup_sample_data()
        
        print("\n" + "=" * 50)
        print("🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Copy .env.example to .env and configure your settings")
        print("2. Run: python app.py")
        print("3. Visit: http://localhost:5000")
        print("4. Admin panel: http://localhost:5000/admin")
        print("\n🐾 Happy pet portal building!")

if __name__ == '__main__':
    main()
