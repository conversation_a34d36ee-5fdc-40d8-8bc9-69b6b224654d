import os
from app import create_app, db
from app.models import Category, Product, Banner, Page, Review, Setting, NewsPost
from flask_migrate import upgrade

app = create_app(os.getenv('FLASK_ENV') or 'default')

@app.shell_context_processor
def make_shell_context():
    return {
        'db': db,
        'Category': Category,
        'Product': Product,
        'Banner': Banner,
        'Page': Page,
        'Review': Review,
        'Setting': Setting,
        'NewsPost': NewsPost
    }

@app.cli.command()
def deploy():
    """Run deployment tasks."""
    # migrate database to latest revision
    upgrade()
    
    # create or update default settings
    create_default_settings()
    
    # create default admin user
    create_default_admin()

def create_default_settings():
    """创建默认设置"""
    default_settings = [
        {
            'key': 'site_title',
            'value_en': 'Pecco Pet Portal - Happy Pets Happy Owner',
            'value_zh': 'Pecco宠物门户 - 快乐宠物快乐主人',
            'description': '网站标题'
        },
        {
            'key': 'site_description',
            'value_en': 'Light luxury pet supplies for a pawsome life',
            'value_zh': '轻奢宠物用品，为美好生活而生',
            'description': '网站描述'
        },
        {
            'key': 'contact_email',
            'value_en': '<EMAIL>',
            'value_zh': '<EMAIL>',
            'description': '联系邮箱'
        },
        {
            'key': 'contact_phone',
            'value_en': '+****************',
            'value_zh': '+86 ************',
            'description': '联系电话'
        },
        {
            'key': 'brand_slogan',
            'value_en': 'Happy Pets Happy Owner',
            'value_zh': '快乐宠物快乐主人',
            'description': '品牌标语'
        }
    ]
    
    for setting_data in default_settings:
        setting = Setting.query.filter_by(key=setting_data['key']).first()
        if not setting:
            setting = Setting(**setting_data)
            db.session.add(setting)
    
    db.session.commit()

def create_default_admin():
    """创建默认管理员用户（如果需要的话）"""
    # 这里可以添加管理员用户创建逻辑
    pass

if __name__ == '__main__':
    app.run(debug=True)
