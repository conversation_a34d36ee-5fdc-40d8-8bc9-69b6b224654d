#!/usr/bin/env python3
"""
Pecco Pet Portal - 安装测试脚本
验证项目是否正确安装和配置
"""

import os
import sys
import requests
import time
from app import create_app, db
from app.models import Category, Product, Banner, Setting

def test_app_creation():
    """测试应用创建"""
    try:
        app = create_app()
        print("✅ Flask应用创建成功")
        return app
    except Exception as e:
        print(f"❌ Flask应用创建失败: {e}")
        return None

def test_database_connection(app):
    """测试数据库连接"""
    try:
        with app.app_context():
            # 测试数据库连接
            db.engine.execute('SELECT 1')
            print("✅ 数据库连接成功")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_models(app):
    """测试数据模型"""
    try:
        with app.app_context():
            # 测试模型查询
            categories = Category.query.count()
            products = Product.query.count()
            banners = Banner.query.count()
            settings = Setting.query.count()
            
            print(f"✅ 数据模型测试成功:")
            print(f"   - 分类: {categories} 个")
            print(f"   - 产品: {products} 个")
            print(f"   - 横幅: {banners} 个")
            print(f"   - 设置: {settings} 个")
            return True
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False

def test_static_files():
    """测试静态文件"""
    static_files = [
        'static/css/style.css',
        'static/js/main.js',
        'static/images/placeholder.svg'
    ]
    
    missing_files = []
    for file_path in static_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少静态文件: {missing_files}")
        return False
    else:
        print("✅ 静态文件检查通过")
        return True

def test_templates():
    """测试模板文件"""
    template_files = [
        'app/templates/base.html',
        'app/templates/main/index.html',
        'app/templates/main/products.html',
        'app/templates/main/product_detail.html',
        'app/templates/includes/header.html',
        'app/templates/includes/footer.html',
        'app/templates/includes/product_card.html'
    ]
    
    missing_templates = []
    for template_path in template_files:
        if not os.path.exists(template_path):
            missing_templates.append(template_path)
    
    if missing_templates:
        print(f"❌ 缺少模板文件: {missing_templates}")
        return False
    else:
        print("✅ 模板文件检查通过")
        return True

def test_directories():
    """测试目录结构"""
    required_dirs = [
        'static/uploads',
        'static/uploads/products',
        'static/uploads/banners',
        'static/uploads/categories',
        'static/uploads/reviews',
        'static/uploads/news',
        'app/templates',
        'app/translations'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"❌ 缺少目录: {missing_dirs}")
        return False
    else:
        print("✅ 目录结构检查通过")
        return True

def test_server_startup():
    """测试服务器启动"""
    try:
        print("🚀 启动测试服务器...")
        app = create_app()
        
        # 在后台启动服务器
        import threading
        import time
        
        def run_server():
            app.run(host='127.0.0.1', port=5001, debug=False, use_reloader=False)
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(3)
        
        # 测试HTTP请求
        response = requests.get('http://127.0.0.1:5001/', timeout=10)
        if response.status_code == 200:
            print("✅ 服务器启动成功，HTTP请求正常")
            return True
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 服务器启动测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🐾 Pecco Pet Portal - 安装测试")
    print("=" * 50)
    
    tests = [
        ("应用创建", test_app_creation),
        ("目录结构", test_directories),
        ("静态文件", test_static_files),
        ("模板文件", test_templates),
    ]
    
    app = None
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        if test_name == "应用创建":
            app = test_func()
            if app:
                passed += 1
        else:
            if test_func():
                passed += 1
    
    # 数据库相关测试
    if app:
        print(f"\n🧪 测试: 数据库连接")
        if test_database_connection(app):
            passed += 1
        total += 1
        
        print(f"\n🧪 测试: 数据模型")
        if test_models(app):
            passed += 1
        total += 1
    
    # 服务器启动测试（可选）
    print(f"\n🧪 测试: 服务器启动")
    if test_server_startup():
        passed += 1
    total += 1
    
    # 测试结果
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目安装成功！")
        print("\n📋 下一步:")
        print("1. 运行: python app.py")
        print("2. 访问: http://localhost:5000")
        print("3. 管理后台: http://localhost:5000/admin")
        return True
    else:
        print("❌ 部分测试失败，请检查安装配置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
