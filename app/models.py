from datetime import datetime
from app import db
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, ForeignKey
from sqlalchemy.orm import relationship
from slugify import slugify

class Category(db.Model):
    """宠物分类模型"""
    __tablename__ = 'categories'
    
    id = Column(Integer, primary_key=True)
    name_en = Column(String(100), nullable=False)
    name_zh = Column(String(100), nullable=False)
    slug = Column(String(120), unique=True, nullable=False)
    description_en = Column(Text)
    description_zh = Column(Text)
    icon_image = Column(String(200))  # 分类图标/头像
    emoji = Column(String(10))  # emoji表情
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联产品
    products = relationship('Product', backref='category', lazy='dynamic')
    
    def __init__(self, **kwargs):
        super(Category, self).__init__(**kwargs)
        if not self.slug and self.name_en:
            self.slug = slugify(self.name_en)
    
    def __repr__(self):
        return f'<Category {self.name_en}>'

class Product(db.Model):
    """产品模型"""
    __tablename__ = 'products'
    
    id = Column(Integer, primary_key=True)
    title_en = Column(String(200), nullable=False)
    title_zh = Column(String(200), nullable=False)
    slug = Column(String(220), unique=True, nullable=False)
    description_en = Column(Text)
    description_zh = Column(Text)
    short_description_en = Column(Text)
    short_description_zh = Column(Text)
    
    # 产品图片
    main_image = Column(String(200))
    gallery_images = Column(Text)  # JSON格式存储多张图片
    
    # 产品属性
    price = Column(Float)
    compare_price = Column(Float)  # 原价（用于显示折扣）
    sku = Column(String(100))
    vendor = Column(String(100))
    
    # 标签和状态
    is_featured = Column(Boolean, default=False)  # 推荐产品
    is_new = Column(Boolean, default=False)  # 新品
    is_hot = Column(Boolean, default=False)  # 热销
    is_active = Column(Boolean, default=True)
    
    # 分类关联
    category_id = Column(Integer, ForeignKey('categories.id'))
    
    # SEO
    meta_title_en = Column(String(200))
    meta_title_zh = Column(String(200))
    meta_description_en = Column(Text)
    meta_description_zh = Column(Text)
    
    # 排序和时间
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, **kwargs):
        super(Product, self).__init__(**kwargs)
        if not self.slug and self.title_en:
            self.slug = slugify(self.title_en)
    
    def __repr__(self):
        return f'<Product {self.title_en}>'

class Banner(db.Model):
    """轮播横幅模型"""
    __tablename__ = 'banners'
    
    id = Column(Integer, primary_key=True)
    title_en = Column(String(200))
    title_zh = Column(String(200))
    subtitle_en = Column(String(300))
    subtitle_zh = Column(String(300))
    description_en = Column(Text)
    description_zh = Column(Text)
    
    # 图片和链接
    image_desktop = Column(String(200), nullable=False)
    image_mobile = Column(String(200))
    link_url = Column(String(300))
    button_text_en = Column(String(100))
    button_text_zh = Column(String(100))
    
    # 状态和排序
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    
    # 显示时间
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Banner {self.title_en}>'

class Page(db.Model):
    """页面内容模型（关于我们、品牌故事等）"""
    __tablename__ = 'pages'
    
    id = Column(Integer, primary_key=True)
    title_en = Column(String(200), nullable=False)
    title_zh = Column(String(200), nullable=False)
    slug = Column(String(220), unique=True, nullable=False)
    content_en = Column(Text)
    content_zh = Column(Text)
    
    # SEO
    meta_title_en = Column(String(200))
    meta_title_zh = Column(String(200))
    meta_description_en = Column(Text)
    meta_description_zh = Column(Text)
    
    # 状态
    is_active = Column(Boolean, default=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, **kwargs):
        super(Page, self).__init__(**kwargs)
        if not self.slug and self.title_en:
            self.slug = slugify(self.title_en)
    
    def __repr__(self):
        return f'<Page {self.title_en}>'

class Review(db.Model):
    """客户评价模型"""
    __tablename__ = 'reviews'

    id = Column(Integer, primary_key=True)
    customer_name = Column(String(100), nullable=False)
    customer_avatar = Column(String(200))
    rating = Column(Integer, nullable=False)  # 1-5星评分
    title_en = Column(String(200))
    title_zh = Column(String(200))
    content_en = Column(Text, nullable=False)
    content_zh = Column(Text, nullable=False)

    # 关联产品（可选）
    product_id = Column(Integer, ForeignKey('products.id'))
    product = relationship('Product', backref='reviews')

    # 状态
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)  # 精选评价

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Review by {self.customer_name}>'

class Setting(db.Model):
    """网站设置模型"""
    __tablename__ = 'settings'

    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False)
    value_en = Column(Text)
    value_zh = Column(Text)
    description = Column(String(300))
    setting_type = Column(String(50), default='text')  # text, textarea, image, boolean

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Setting {self.key}>'

class NewsPost(db.Model):
    """新闻资讯模型"""
    __tablename__ = 'news_posts'

    id = Column(Integer, primary_key=True)
    title_en = Column(String(200), nullable=False)
    title_zh = Column(String(200), nullable=False)
    slug = Column(String(220), unique=True, nullable=False)
    excerpt_en = Column(Text)
    excerpt_zh = Column(Text)
    content_en = Column(Text)
    content_zh = Column(Text)

    # 图片
    featured_image = Column(String(200))

    # 状态
    is_published = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)

    # SEO
    meta_title_en = Column(String(200))
    meta_title_zh = Column(String(200))
    meta_description_en = Column(Text)
    meta_description_zh = Column(Text)

    # 时间
    published_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, **kwargs):
        super(NewsPost, self).__init__(**kwargs)
        if not self.slug and self.title_en:
            self.slug = slugify(self.title_en)

    def __repr__(self):
        return f'<NewsPost {self.title_en}>'
