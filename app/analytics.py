"""
网站分析和性能监控工具
"""

import time
import logging
from functools import wraps
from flask import request, g, current_app
from datetime import datetime, timedelta
import json
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控类"""
    
    def __init__(self):
        self.metrics = {}
    
    def start_timer(self, name):
        """开始计时"""
        g.start_time = time.time()
        g.timer_name = name
    
    def end_timer(self):
        """结束计时"""
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            timer_name = getattr(g, 'timer_name', 'unknown')
            self.record_metric(timer_name, duration)
            return duration
        return None
    
    def record_metric(self, name, value):
        """记录指标"""
        if name not in self.metrics:
            self.metrics[name] = []
        
        self.metrics[name].append({
            'value': value,
            'timestamp': datetime.utcnow().isoformat()
        })
        
        # 保持最近100条记录
        if len(self.metrics[name]) > 100:
            self.metrics[name] = self.metrics[name][-100:]
    
    def get_average(self, name):
        """获取平均值"""
        if name not in self.metrics or not self.metrics[name]:
            return 0
        
        values = [m['value'] for m in self.metrics[name]]
        return sum(values) / len(values)
    
    def get_stats(self, name):
        """获取统计信息"""
        if name not in self.metrics or not self.metrics[name]:
            return {}
        
        values = [m['value'] for m in self.metrics[name]]
        return {
            'count': len(values),
            'average': sum(values) / len(values),
            'min': min(values),
            'max': max(values),
            'latest': values[-1] if values else 0
        }

# 全局性能监控实例
performance_monitor = PerformanceMonitor()

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            # 记录性能指标
            endpoint = request.endpoint or 'unknown'
            performance_monitor.record_metric(f'response_time_{endpoint}', duration)
            
            # 记录慢查询
            if duration > 1.0:  # 超过1秒的请求
                logger.warning(f'Slow request: {endpoint} took {duration:.2f}s')
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            performance_monitor.record_metric('error_response_time', duration)
            logger.error(f'Error in {request.endpoint}: {str(e)}')
            raise
    
    return wrapper

class PageViewTracker:
    """页面访问跟踪"""
    
    def __init__(self):
        self.views = {}
        self.log_file = 'logs/page_views.log'
        self.ensure_log_directory()
    
    def ensure_log_directory(self):
        """确保日志目录存在"""
        log_dir = os.path.dirname(self.log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    def track_view(self, page, user_agent=None, ip_address=None):
        """跟踪页面访问"""
        timestamp = datetime.utcnow()
        
        # 内存中的统计
        if page not in self.views:
            self.views[page] = []
        
        view_data = {
            'timestamp': timestamp.isoformat(),
            'user_agent': user_agent,
            'ip_address': ip_address
        }
        
        self.views[page].append(view_data)
        
        # 保持最近1000条记录
        if len(self.views[page]) > 1000:
            self.views[page] = self.views[page][-1000:]
        
        # 写入日志文件
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                log_entry = {
                    'page': page,
                    'timestamp': timestamp.isoformat(),
                    'user_agent': user_agent,
                    'ip_address': ip_address
                }
                f.write(json.dumps(log_entry) + '\n')
        except Exception as e:
            logger.error(f'Failed to write page view log: {e}')
    
    def get_popular_pages(self, limit=10):
        """获取热门页面"""
        page_counts = {}
        
        for page, views in self.views.items():
            page_counts[page] = len(views)
        
        # 按访问量排序
        sorted_pages = sorted(page_counts.items(), key=lambda x: x[1], reverse=True)
        return sorted_pages[:limit]
    
    def get_recent_views(self, hours=24):
        """获取最近的访问记录"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_views = []
        
        for page, views in self.views.items():
            for view in views:
                view_time = datetime.fromisoformat(view['timestamp'])
                if view_time > cutoff_time:
                    recent_views.append({
                        'page': page,
                        **view
                    })
        
        return sorted(recent_views, key=lambda x: x['timestamp'], reverse=True)

# 全局页面访问跟踪实例
page_tracker = PageViewTracker()

def track_page_view():
    """跟踪当前页面访问"""
    page = request.endpoint or request.path
    user_agent = request.headers.get('User-Agent', '')
    ip_address = request.remote_addr
    
    page_tracker.track_view(page, user_agent, ip_address)

class ErrorTracker:
    """错误跟踪"""
    
    def __init__(self):
        self.errors = []
        self.log_file = 'logs/errors.log'
        self.ensure_log_directory()
    
    def ensure_log_directory(self):
        """确保日志目录存在"""
        log_dir = os.path.dirname(self.log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    def track_error(self, error, endpoint=None, user_agent=None, ip_address=None):
        """跟踪错误"""
        timestamp = datetime.utcnow()
        
        error_data = {
            'timestamp': timestamp.isoformat(),
            'error': str(error),
            'error_type': type(error).__name__,
            'endpoint': endpoint,
            'user_agent': user_agent,
            'ip_address': ip_address
        }
        
        self.errors.append(error_data)
        
        # 保持最近500条错误记录
        if len(self.errors) > 500:
            self.errors = self.errors[-500:]
        
        # 写入日志文件
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(error_data) + '\n')
        except Exception as e:
            logger.error(f'Failed to write error log: {e}')
    
    def get_error_stats(self):
        """获取错误统计"""
        if not self.errors:
            return {}
        
        error_types = {}
        endpoints = {}
        
        for error in self.errors:
            error_type = error.get('error_type', 'Unknown')
            endpoint = error.get('endpoint', 'Unknown')
            
            error_types[error_type] = error_types.get(error_type, 0) + 1
            endpoints[endpoint] = endpoints.get(endpoint, 0) + 1
        
        return {
            'total_errors': len(self.errors),
            'error_types': error_types,
            'endpoints': endpoints,
            'recent_errors': self.errors[-10:]  # 最近10个错误
        }

# 全局错误跟踪实例
error_tracker = ErrorTracker()

def get_analytics_dashboard_data():
    """获取分析仪表板数据"""
    return {
        'performance': {
            'response_times': {
                name: performance_monitor.get_stats(name)
                for name in performance_monitor.metrics.keys()
                if name.startswith('response_time_')
            }
        },
        'page_views': {
            'popular_pages': page_tracker.get_popular_pages(),
            'recent_views_count': len(page_tracker.get_recent_views()),
            'total_pages_tracked': len(page_tracker.views)
        },
        'errors': error_tracker.get_error_stats()
    }

def log_user_action(action, details=None):
    """记录用户行为"""
    log_data = {
        'timestamp': datetime.utcnow().isoformat(),
        'action': action,
        'details': details,
        'endpoint': request.endpoint,
        'user_agent': request.headers.get('User-Agent', ''),
        'ip_address': request.remote_addr
    }
    
    try:
        with open('logs/user_actions.log', 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_data) + '\n')
    except Exception as e:
        logger.error(f'Failed to log user action: {e}')

# 中间件函数
def init_analytics(app):
    """初始化分析中间件"""
    
    @app.before_request
    def before_request():
        """请求前处理"""
        g.start_time = time.time()
        track_page_view()
    
    @app.after_request
    def after_request(response):
        """请求后处理"""
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            endpoint = request.endpoint or 'unknown'
            performance_monitor.record_metric(f'response_time_{endpoint}', duration)
        
        return response
    
    @app.errorhandler(Exception)
    def handle_error(error):
        """错误处理"""
        error_tracker.track_error(
            error,
            endpoint=request.endpoint,
            user_agent=request.headers.get('User-Agent', ''),
            ip_address=request.remote_addr
        )
        
        # 重新抛出错误，让Flask的默认错误处理器处理
        raise error
