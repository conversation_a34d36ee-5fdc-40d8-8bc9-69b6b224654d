"""
实用工具函数
"""
import os
import json
from flask import current_app
from werkzeug.utils import secure_filename
from PIL import Image
import uuid

def allowed_file(filename, allowed_extensions=None):
    """检查文件扩展名是否允许"""
    if allowed_extensions is None:
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_uploaded_file(file, upload_folder, max_size=(800, 600), quality=85):
    """
    保存上传的图片文件
    
    Args:
        file: 上传的文件对象
        upload_folder: 上传目录
        max_size: 最大尺寸 (width, height)
        quality: 图片质量 (1-100)
    
    Returns:
        str: 保存的文件名，如果失败返回None
    """
    if not file or not allowed_file(file.filename):
        return None
    
    # 生成唯一文件名
    filename = secure_filename(file.filename)
    name, ext = os.path.splitext(filename)
    unique_filename = f"{uuid.uuid4().hex}{ext}"
    
    # 确保上传目录存在
    upload_path = os.path.join(current_app.static_folder, upload_folder)
    os.makedirs(upload_path, exist_ok=True)
    
    file_path = os.path.join(upload_path, unique_filename)
    
    try:
        # 保存并优化图片
        image = Image.open(file.stream)
        
        # 转换为RGB（如果是RGBA）
        if image.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        
        # 调整大小
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # 保存
        image.save(file_path, 'JPEG', quality=quality, optimize=True)
        
        return unique_filename
    
    except Exception as e:
        current_app.logger.error(f"Error saving file: {e}")
        return None

def delete_file(filename, folder):
    """删除文件"""
    if not filename:
        return
    
    file_path = os.path.join(current_app.static_folder, folder, filename)
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception as e:
        current_app.logger.error(f"Error deleting file: {e}")

def format_price(price, currency='$'):
    """格式化价格"""
    if price is None:
        return ''
    return f"{currency}{price:.2f}"

def truncate_text(text, length=100, suffix='...'):
    """截断文本"""
    if not text:
        return ''
    
    if len(text) <= length:
        return text
    
    return text[:length].rsplit(' ', 1)[0] + suffix

def get_image_url(filename, folder, default='placeholder.jpg'):
    """获取图片URL"""
    from flask import url_for
    
    if filename:
        return url_for('static', filename=f'{folder}/{filename}')
    else:
        return url_for('static', filename=f'images/{default}')

def parse_gallery_images(gallery_json):
    """解析图片库JSON"""
    if not gallery_json:
        return []
    
    try:
        if isinstance(gallery_json, str):
            return json.loads(gallery_json)
        return gallery_json
    except (json.JSONDecodeError, TypeError):
        return []

def create_gallery_json(image_list):
    """创建图片库JSON"""
    if not image_list:
        return None
    
    # 过滤空值
    images = [img for img in image_list if img]
    return json.dumps(images) if images else None

def get_localized_content(obj, field_name, locale='en'):
    """获取本地化内容"""
    if locale == 'zh':
        zh_field = f"{field_name}_zh"
        if hasattr(obj, zh_field) and getattr(obj, zh_field):
            return getattr(obj, zh_field)
    
    en_field = f"{field_name}_en"
    if hasattr(obj, en_field):
        return getattr(obj, en_field)
    
    return ''

def slugify_chinese(text):
    """为中文文本创建slug"""
    from slugify import slugify
    import re
    
    if not text:
        return ''
    
    # 如果包含中文，使用拼音
    if re.search(r'[\u4e00-\u9fff]', text):
        try:
            from pypinyin import lazy_pinyin, Style
            pinyin_list = lazy_pinyin(text, style=Style.NORMAL)
            text = '-'.join(pinyin_list)
        except ImportError:
            # 如果没有pypinyin，使用简单的处理
            text = re.sub(r'[\u4e00-\u9fff]', '', text)
    
    return slugify(text)

def calculate_discount_percentage(original_price, sale_price):
    """计算折扣百分比"""
    if not original_price or not sale_price or original_price <= sale_price:
        return 0
    
    discount = ((original_price - sale_price) / original_price) * 100
    return round(discount)

def generate_meta_description(content, max_length=160):
    """从内容生成meta描述"""
    if not content:
        return ''
    
    # 移除HTML标签
    import re
    clean_content = re.sub(r'<[^>]+>', '', content)
    
    # 截断到合适长度
    return truncate_text(clean_content, max_length)

def validate_email(email):
    """验证邮箱格式"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def get_client_ip(request):
    """获取客户端IP地址"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def create_breadcrumb(items):
    """创建面包屑导航"""
    breadcrumb = []
    for item in items:
        if isinstance(item, dict):
            breadcrumb.append(item)
        else:
            breadcrumb.append({'name': item, 'url': None})
    return breadcrumb

class PaginationHelper:
    """分页辅助类"""
    
    @staticmethod
    def get_pagination_info(pagination):
        """获取分页信息"""
        return {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev,
            'next_num': pagination.next_num,
            'prev_num': pagination.prev_num,
            'start_index': (pagination.page - 1) * pagination.per_page + 1,
            'end_index': min(pagination.page * pagination.per_page, pagination.total)
        }
    
    @staticmethod
    def get_page_range(pagination, delta=2):
        """获取页码范围"""
        current_page = pagination.page
        total_pages = pagination.pages
        
        start = max(1, current_page - delta)
        end = min(total_pages, current_page + delta)
        
        # 确保显示足够的页码
        if end - start < 2 * delta:
            if start == 1:
                end = min(total_pages, start + 2 * delta)
            elif end == total_pages:
                start = max(1, end - 2 * delta)
        
        return list(range(start, end + 1))
