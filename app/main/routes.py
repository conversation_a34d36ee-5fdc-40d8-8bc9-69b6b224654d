from flask import render_template, request, jsonify, session, redirect, url_for, flash, Response
from flask_babel import _, get_locale
from app.main import bp
from app.models import Category, Product, Banner, Page, Review, Setting, NewsPost
from app import db
from sqlalchemy import desc, func
from app.seo import generate_sitemap, generate_robots_txt, get_structured_data_for_product, get_structured_data_for_organization
import json

@bp.context_processor
def inject_template_vars():
    """注入模板全局变量"""
    return {
        'get_categories': get_categories,
        'get_setting': get_setting,
        'get_locale': get_locale,
        'get_structured_data_for_product': get_structured_data_for_product,
        'get_structured_data_for_organization': get_structured_data_for_organization
    }

def get_categories():
    """获取所有活跃的分类"""
    return Category.query.filter_by(is_active=True).order_by(Category.sort_order, Category.name_en).all()

def get_setting(key, default=''):
    """获取设置值"""
    setting = Setting.query.filter_by(key=key).first()
    if setting:
        if get_locale() == 'zh' and setting.value_zh:
            return setting.value_zh
        return setting.value_en or default
    return default

@bp.route('/')
def index():
    """首页"""
    # 获取轮播图
    banners = Banner.query.filter_by(is_active=True).order_by(Banner.sort_order).limit(5).all()
    
    # 获取分类（带产品数量）
    categories = db.session.query(
        Category,
        func.count(Product.id).label('product_count')
    ).outerjoin(Product, (Product.category_id == Category.id) & (Product.is_active == True))\
     .filter(Category.is_active == True)\
     .group_by(Category.id)\
     .order_by(Category.sort_order, Category.name_en)\
     .limit(8).all()
    
    # 获取推荐产品
    featured_products = Product.query.filter_by(is_active=True, is_featured=True)\
                                   .order_by(Product.sort_order, desc(Product.created_at))\
                                   .limit(8).all()
    
    # 获取新品
    new_products = Product.query.filter_by(is_active=True, is_new=True)\
                                .order_by(desc(Product.created_at))\
                                .limit(4).all()
    
    # 获取热销产品
    hot_products = Product.query.filter_by(is_active=True, is_hot=True)\
                                .order_by(Product.sort_order, desc(Product.created_at))\
                                .limit(4).all()
    
    # 获取客户评价
    reviews = Review.query.filter_by(is_active=True, is_featured=True)\
                         .order_by(desc(Review.created_at))\
                         .limit(6).all()
    
    # 获取最新新闻
    latest_news = NewsPost.query.filter_by(is_published=True)\
                                .order_by(desc(NewsPost.published_at))\
                                .limit(3).all()
    
    return render_template('main/index.html',
                         banners=banners,
                         categories=categories,
                         featured_products=featured_products,
                         new_products=new_products,
                         hot_products=hot_products,
                         reviews=reviews,
                         latest_news=latest_news)

@bp.route('/products')
def products():
    """产品列表页"""
    page = request.args.get('page', 1, type=int)
    category_slug = request.args.get('category')
    sort_by = request.args.get('sort', 'newest')
    
    # 基础查询
    query = Product.query.filter_by(is_active=True)
    
    # 分类筛选
    if category_slug:
        category = Category.query.filter_by(slug=category_slug, is_active=True).first_or_404()
        query = query.filter_by(category_id=category.id)
    
    # 排序
    if sort_by == 'newest':
        query = query.order_by(desc(Product.created_at))
    elif sort_by == 'price_low':
        query = query.order_by(Product.price.asc())
    elif sort_by == 'price_high':
        query = query.order_by(Product.price.desc())
    elif sort_by == 'name':
        if get_locale() == 'zh':
            query = query.order_by(Product.title_zh)
        else:
            query = query.order_by(Product.title_en)
    else:
        query = query.order_by(Product.sort_order, desc(Product.created_at))
    
    # 分页
    from config import Config
    products = query.paginate(
        page=page,
        per_page=Config.PRODUCTS_PER_PAGE,
        error_out=False
    )
    
    return render_template('main/products.html',
                         products=products,
                         current_category=category_slug)

@bp.route('/product/<slug>')
def product_detail(slug):
    """产品详情页"""
    product = Product.query.filter_by(slug=slug, is_active=True).first_or_404()
    
    # 相关产品
    related_products = Product.query.filter(
        Product.category_id == product.category_id,
        Product.id != product.id,
        Product.is_active == True
    ).limit(4).all()
    
    # 产品评价
    product_reviews = Review.query.filter_by(product_id=product.id, is_active=True)\
                                 .order_by(desc(Review.created_at))\
                                 .limit(10).all()
    
    return render_template('main/product_detail.html',
                         product=product,
                         related_products=related_products,
                         reviews=product_reviews)

@bp.route('/category/<slug>')
def category(slug):
    """分类页面"""
    category = Category.query.filter_by(slug=slug, is_active=True).first_or_404()
    page = request.args.get('page', 1, type=int)
    sort_by = request.args.get('sort', 'newest')
    
    # 获取分类下的产品
    query = Product.query.filter_by(category_id=category.id, is_active=True)
    
    # 排序
    if sort_by == 'newest':
        query = query.order_by(desc(Product.created_at))
    elif sort_by == 'price_low':
        query = query.order_by(Product.price.asc())
    elif sort_by == 'price_high':
        query = query.order_by(Product.price.desc())
    else:
        query = query.order_by(Product.sort_order, desc(Product.created_at))
    
    # 分页
    from config import Config
    products = query.paginate(
        page=page,
        per_page=Config.PRODUCTS_PER_PAGE,
        error_out=False
    )
    
    return render_template('main/category.html',
                         category=category,
                         products=products)

@bp.route('/page/<slug>')
def page(slug):
    """静态页面"""
    page = Page.query.filter_by(slug=slug, is_active=True).first_or_404()
    return render_template('main/page.html', page=page)

@bp.route('/news')
def news():
    """新闻列表"""
    page = request.args.get('page', 1, type=int)
    
    from config import Config
    posts = NewsPost.query.filter_by(is_published=True)\
                         .order_by(desc(NewsPost.published_at))\
                         .paginate(
                             page=page,
                             per_page=Config.POSTS_PER_PAGE,
                             error_out=False
                         )
    
    return render_template('main/news.html', posts=posts)

@bp.route('/news/<slug>')
def news_detail(slug):
    """新闻详情"""
    post = NewsPost.query.filter_by(slug=slug, is_published=True).first_or_404()
    
    # 相关新闻
    related_posts = NewsPost.query.filter(
        NewsPost.id != post.id,
        NewsPost.is_published == True
    ).order_by(desc(NewsPost.published_at)).limit(3).all()
    
    return render_template('main/news_detail.html',
                         post=post,
                         related_posts=related_posts)

@bp.route('/contact')
def contact():
    """联系我们"""
    return render_template('main/contact.html')

@bp.route('/search')
def search():
    """搜索"""
    query = request.args.get('q', '').strip()
    page = request.args.get('page', 1, type=int)
    
    if not query:
        return redirect(url_for('main.products'))
    
    # 搜索产品
    if get_locale() == 'zh':
        search_query = Product.query.filter(
            Product.is_active == True,
            db.or_(
                Product.title_zh.contains(query),
                Product.description_zh.contains(query),
                Product.short_description_zh.contains(query)
            )
        )
    else:
        search_query = Product.query.filter(
            Product.is_active == True,
            db.or_(
                Product.title_en.contains(query),
                Product.description_en.contains(query),
                Product.short_description_en.contains(query)
            )
        )
    
    from config import Config
    products = search_query.order_by(desc(Product.created_at))\
                          .paginate(
                              page=page,
                              per_page=Config.PRODUCTS_PER_PAGE,
                              error_out=False
                          )
    
    return render_template('main/search.html',
                         products=products,
                         query=query)

@bp.route('/sitemap.xml')
def sitemap():
    """生成XML sitemap"""
    sitemap_xml = generate_sitemap()
    return Response(sitemap_xml, mimetype='application/xml')

@bp.route('/robots.txt')
def robots():
    """生成robots.txt"""
    robots_content = generate_robots_txt()
    return Response(robots_content, mimetype='text/plain')

@bp.route('/manifest.json')
def manifest():
    """生成PWA manifest"""
    manifest_data = {
        "name": "Pecco Pet Portal",
        "short_name": "Pecco",
        "description": "Light luxury pet supplies for a pawsome life",
        "start_url": "/",
        "display": "standalone",
        "background_color": "#faf8f5",
        "theme_color": "#e1732c",
        "icons": [
            {
                "src": url_for('static', filename='images/icon-192.png'),
                "sizes": "192x192",
                "type": "image/png"
            },
            {
                "src": url_for('static', filename='images/icon-512.png'),
                "sizes": "512x512",
                "type": "image/png"
            }
        ]
    }
    return jsonify(manifest_data)
