import os
from flask import Flask, request, session
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migra<PERSON>
from flask_admin import Admin
from flask_babel import Babel
from config import config

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
admin = Admin()
babel = Babel()

def create_app(config_name=None):
    """Application factory pattern"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions with app
    db.init_app(app)
    migrate.init_app(app, db)
    admin.init_app(app, name='Pecco Pet Portal Admin', template_mode='bootstrap4')
    babel.init_app(app)
    
    # Create upload directories
    upload_dir = os.path.join(app.instance_path, app.config['UPLOAD_FOLDER'])
    os.makedirs(upload_dir, exist_ok=True)
    os.makedirs(os.path.join(upload_dir, 'products'), exist_ok=True)
    os.makedirs(os.path.join(upload_dir, 'banners'), exist_ok=True)
    os.makedirs(os.path.join(upload_dir, 'categories'), exist_ok=True)
    
    # Language selector
    @babel.localeselector
    def get_locale():
        # 1. Check URL parameter
        if 'lang' in request.args:
            session['language'] = request.args['lang']
        
        # 2. Check session
        if 'language' in session and session['language'] in app.config['LANGUAGES']:
            return session['language']
        
        # 3. Check browser preference
        return request.accept_languages.best_match(app.config['LANGUAGES']) or app.config['DEFAULT_LANGUAGE']
    
    # Register blueprints
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')
    
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # Import models to ensure they are registered with SQLAlchemy
    from app import models
    
    # Setup admin views
    from app.admin.views import setup_admin_views
    setup_admin_views(admin, db)
    
    return app
