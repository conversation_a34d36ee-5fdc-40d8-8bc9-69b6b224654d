from flask import jsonify, request
from app.api import bp
from app.models import Category, Product, Banner, Review
from app import db

@bp.route('/categories')
def get_categories():
    """获取所有分类"""
    categories = Category.query.filter_by(is_active=True).order_by(Category.sort_order).all()
    return jsonify([{
        'id': cat.id,
        'name_en': cat.name_en,
        'name_zh': cat.name_zh,
        'slug': cat.slug,
        'emoji': cat.emoji,
        'icon_image': cat.icon_image
    } for cat in categories])

@bp.route('/products')
def get_products():
    """获取产品列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 12, type=int)
    category_id = request.args.get('category_id', type=int)
    
    query = Product.query.filter_by(is_active=True)
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    products = query.order_by(Product.sort_order, Product.created_at.desc())\
                   .paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'products': [{
            'id': p.id,
            'title_en': p.title_en,
            'title_zh': p.title_zh,
            'slug': p.slug,
            'price': float(p.price) if p.price else None,
            'compare_price': float(p.compare_price) if p.compare_price else None,
            'main_image': p.main_image,
            'is_featured': p.is_featured,
            'is_new': p.is_new,
            'is_hot': p.is_hot,
            'vendor': p.vendor
        } for p in products.items],
        'pagination': {
            'page': products.page,
            'pages': products.pages,
            'per_page': products.per_page,
            'total': products.total,
            'has_next': products.has_next,
            'has_prev': products.has_prev
        }
    })

@bp.route('/product/<slug>')
def get_product(slug):
    """获取单个产品详情"""
    product = Product.query.filter_by(slug=slug, is_active=True).first()
    if not product:
        return jsonify({'error': 'Product not found'}), 404
    
    return jsonify({
        'id': product.id,
        'title_en': product.title_en,
        'title_zh': product.title_zh,
        'slug': product.slug,
        'description_en': product.description_en,
        'description_zh': product.description_zh,
        'short_description_en': product.short_description_en,
        'short_description_zh': product.short_description_zh,
        'price': float(product.price) if product.price else None,
        'compare_price': float(product.compare_price) if product.compare_price else None,
        'main_image': product.main_image,
        'gallery_images': product.gallery_images,
        'sku': product.sku,
        'vendor': product.vendor,
        'is_featured': product.is_featured,
        'is_new': product.is_new,
        'is_hot': product.is_hot,
        'category': {
            'id': product.category.id,
            'name_en': product.category.name_en,
            'name_zh': product.category.name_zh,
            'slug': product.category.slug
        } if product.category else None
    })

@bp.route('/banners')
def get_banners():
    """获取轮播图"""
    banners = Banner.query.filter_by(is_active=True).order_by(Banner.sort_order).all()
    return jsonify([{
        'id': banner.id,
        'title_en': banner.title_en,
        'title_zh': banner.title_zh,
        'subtitle_en': banner.subtitle_en,
        'subtitle_zh': banner.subtitle_zh,
        'image_desktop': banner.image_desktop,
        'image_mobile': banner.image_mobile,
        'link_url': banner.link_url,
        'button_text_en': banner.button_text_en,
        'button_text_zh': banner.button_text_zh
    } for banner in banners])

@bp.route('/reviews')
def get_reviews():
    """获取评价列表"""
    product_id = request.args.get('product_id', type=int)
    featured_only = request.args.get('featured', 'false').lower() == 'true'
    
    query = Review.query.filter_by(is_active=True)
    
    if product_id:
        query = query.filter_by(product_id=product_id)
    
    if featured_only:
        query = query.filter_by(is_featured=True)
    
    reviews = query.order_by(Review.created_at.desc()).limit(20).all()
    
    return jsonify([{
        'id': review.id,
        'customer_name': review.customer_name,
        'customer_avatar': review.customer_avatar,
        'rating': review.rating,
        'title_en': review.title_en,
        'title_zh': review.title_zh,
        'content_en': review.content_en,
        'content_zh': review.content_zh,
        'created_at': review.created_at.isoformat(),
        'product_id': review.product_id
    } for review in reviews])

@bp.route('/search')
def search_products():
    """搜索产品"""
    query = request.args.get('q', '').strip()
    lang = request.args.get('lang', 'en')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 12, type=int)
    
    if not query:
        return jsonify({'error': 'Query parameter is required'}), 400
    
    if lang == 'zh':
        search_query = Product.query.filter(
            Product.is_active == True,
            db.or_(
                Product.title_zh.contains(query),
                Product.description_zh.contains(query),
                Product.short_description_zh.contains(query)
            )
        )
    else:
        search_query = Product.query.filter(
            Product.is_active == True,
            db.or_(
                Product.title_en.contains(query),
                Product.description_en.contains(query),
                Product.short_description_en.contains(query)
            )
        )
    
    products = search_query.order_by(Product.created_at.desc())\
                          .paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'query': query,
        'products': [{
            'id': p.id,
            'title_en': p.title_en,
            'title_zh': p.title_zh,
            'slug': p.slug,
            'price': float(p.price) if p.price else None,
            'main_image': p.main_image,
            'short_description_en': p.short_description_en,
            'short_description_zh': p.short_description_zh
        } for p in products.items],
        'pagination': {
            'page': products.page,
            'pages': products.pages,
            'per_page': products.per_page,
            'total': products.total
        }
    })
