{% extends "base.html" %}

{% block title %}{{ _('Pecco Pet Portal - Happy Pets Happy Owner') }}{% endblock %}

{% block content %}
<!-- Hero Banner Section -->
{% if banners %}
<section class="hero-banner">
    <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
        <div class="carousel-indicators">
            {% for banner in banners %}
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="{{ loop.index0 }}" 
                    {% if loop.first %}class="active"{% endif %}></button>
            {% endfor %}
        </div>
        
        <div class="carousel-inner">
            {% for banner in banners %}
            <div class="carousel-item {% if loop.first %}active{% endif %}">
                <div class="hero-slide" style="background-image: url('{{ url_for('static', filename='uploads/banners/' + banner.image_desktop) }}');">
                    <div class="hero-overlay"></div>
                    <div class="container">
                        <div class="hero-content animate-on-scroll">
                            {% if banner.title_zh and get_locale() == 'zh' %}
                                <h1 class="hero-title">{{ banner.title_zh }}</h1>
                            {% else %}
                                <h1 class="hero-title">{{ banner.title_en }}</h1>
                            {% endif %}
                            
                            {% if banner.subtitle_zh and get_locale() == 'zh' %}
                                <p class="hero-subtitle">{{ banner.subtitle_zh }}</p>
                            {% elif banner.subtitle_en %}
                                <p class="hero-subtitle">{{ banner.subtitle_en }}</p>
                            {% endif %}
                            
                            {% if banner.link_url %}
                            <div class="hero-actions mt-4">
                                <a href="{{ banner.link_url }}" class="btn btn-primary btn-lg">
                                    {% if banner.button_text_zh and get_locale() == 'zh' %}
                                        {{ banner.button_text_zh }}
                                    {% else %}
                                        {{ banner.button_text_en or _('Learn More') }}
                                    {% endif %}
                                    <i class="fas fa-arrow-right ms-2"></i>
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
        </button>
    </div>
</section>
{% endif %}

<!-- Pet Categories Section -->
<section class="section-padding bg-light-gray">
    <div class="container">
        <div class="section-title animate-on-scroll">
            <h2>🐾 {{ _('Shop by Pet Type') }}</h2>
            <p>{{ _('Find the perfect products for your beloved companions') }}</p>
        </div>
        
        <div class="row g-4">
            {% for category, product_count in categories %}
            <div class="col-lg-3 col-md-4 col-sm-6">
                <a href="{{ url_for('main.category', slug=category.slug) }}" class="category-card d-block animate-on-scroll">
                    <div class="category-icon">
                        {% if category.emoji %}
                            {{ category.emoji }}
                        {% elif category.icon_image %}
                            <img src="{{ url_for('static', filename='uploads/categories/' + category.icon_image) }}" 
                                 alt="{{ category.name_en }}" class="w-100 h-100 object-fit-cover rounded-circle">
                        {% else %}
                            🐾
                        {% endif %}
                    </div>
                    <h5 class="category-name">
                        {{ category.name_zh if get_locale() == 'zh' else category.name_en }}
                    </h5>
                    <p class="category-count">{{ product_count }} {{ _('products') }}</p>
                </a>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-5">
            <a href="{{ url_for('main.products') }}" class="btn btn-outline-primary btn-lg">
                {{ _('View All Products') }} <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
{% if featured_products %}
<section class="section-padding">
    <div class="container">
        <div class="section-title animate-on-scroll">
            <h2>✨ {{ _('Featured Products') }}</h2>
            <p>{{ _('Handpicked luxury items for your precious pets') }}</p>
        </div>
        
        <div class="row g-4">
            {% for product in featured_products %}
            <div class="col-lg-3 col-md-4 col-sm-6">
                {% include 'includes/product_card.html' %}
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- New & Hot Products Section -->
<section class="section-padding bg-cream">
    <div class="container">
        <div class="row">
            <!-- New Products -->
            {% if new_products %}
            <div class="col-lg-6 mb-5 mb-lg-0">
                <div class="section-title text-start animate-on-scroll">
                    <h3>🆕 {{ _('New Arrivals') }}</h3>
                    <p>{{ _('Latest additions to our luxury collection') }}</p>
                </div>
                
                <div class="row g-3">
                    {% for product in new_products %}
                    <div class="col-md-6">
                        {% include 'includes/product_card.html' %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <!-- Hot Products -->
            {% if hot_products %}
            <div class="col-lg-6">
                <div class="section-title text-start animate-on-scroll">
                    <h3>🔥 {{ _('Hot Sellers') }}</h3>
                    <p>{{ _('Most popular items loved by pet owners') }}</p>
                </div>
                
                <div class="row g-3">
                    {% for product in hot_products %}
                    <div class="col-md-6">
                        {% include 'includes/product_card.html' %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Customer Reviews Section -->
{% if reviews %}
<section class="section-padding">
    <div class="container">
        <div class="section-title animate-on-scroll">
            <h2>💝 {{ _('Happy Customers') }}</h2>
            <p>{{ _('See what pet parents are saying about our products') }}</p>
        </div>
        
        <div class="row g-4">
            {% for review in reviews %}
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 animate-on-scroll">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            {% if review.customer_avatar %}
                            <img src="{{ url_for('static', filename='uploads/reviews/' + review.customer_avatar) }}" 
                                 alt="{{ review.customer_name }}" class="rounded-circle me-3" width="50" height="50">
                            {% else %}
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                                 style="width: 50px; height: 50px;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            {% endif %}
                            <div>
                                <h6 class="mb-0">{{ review.customer_name }}</h6>
                                <div class="text-warning">
                                    {% for i in range(review.rating) %}
                                    <i class="fas fa-star"></i>
                                    {% endfor %}
                                    {% for i in range(5 - review.rating) %}
                                    <i class="far fa-star"></i>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        
                        {% if review.title_zh and get_locale() == 'zh' %}
                        <h6 class="card-title">{{ review.title_zh }}</h6>
                        {% elif review.title_en %}
                        <h6 class="card-title">{{ review.title_en }}</h6>
                        {% endif %}
                        
                        <p class="card-text text-muted">
                            {{ review.content_zh if get_locale() == 'zh' else review.content_en }}
                        </p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Latest News Section -->
{% if latest_news %}
<section class="section-padding bg-light-gray">
    <div class="container">
        <div class="section-title animate-on-scroll">
            <h2>📰 {{ _('Latest News') }}</h2>
            <p>{{ _('Stay updated with pet care tips and product updates') }}</p>
        </div>
        
        <div class="row g-4">
            {% for post in latest_news %}
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 animate-on-scroll">
                    {% if post.featured_image %}
                    <img src="{{ url_for('static', filename='uploads/news/' + post.featured_image) }}" 
                         class="card-img-top" alt="{{ post.title_en }}">
                    {% endif %}
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="{{ url_for('main.news_detail', slug=post.slug) }}" class="text-decoration-none">
                                {{ post.title_zh if get_locale() == 'zh' else post.title_en }}
                            </a>
                        </h5>
                        <p class="card-text text-muted">
                            {{ post.excerpt_zh if get_locale() == 'zh' else post.excerpt_en }}
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                {{ moment(post.published_at).format('MMMM DD, YYYY') }}
                            </small>
                            <a href="{{ url_for('main.news_detail', slug=post.slug) }}" class="btn btn-sm btn-outline-primary">
                                {{ _('Read More') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-5">
            <a href="{{ url_for('main.news') }}" class="btn btn-outline-secondary btn-lg">
                {{ _('View All News') }} <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>
{% endif %}

<!-- Call to Action Section -->
<section class="section-padding bg-primary text-white">
    <div class="container text-center">
        <div class="animate-on-scroll">
            <h2 class="mb-4">🐾 {{ _('Ready to Pamper Your Pet?') }}</h2>
            <p class="lead mb-4">
                {{ _('Discover our complete collection of luxury pet supplies designed with love and care.') }}
            </p>
            <div class="d-flex flex-wrap justify-content-center gap-3">
                <a href="{{ url_for('main.products') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-shopping-bag me-2"></i>
                    {{ _('Shop Now') }}
                </a>
                <a href="{{ url_for('main.contact') }}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-phone me-2"></i>
                    {{ _('Contact Us') }}
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}
