{% extends "base.html" %}

{% block title %}{{ page.title_zh if get_locale() == 'zh' else page.title_en }} - {{ _('Pecco Pet Portal') }}{% endblock %}

{% block meta_description %}{{ page.meta_description_zh if get_locale() == 'zh' else page.meta_description_en }}{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header bg-light-gray py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">{{ _('Home') }}</a></li>
                        <li class="breadcrumb-item active">{{ page.title_zh if get_locale() == 'zh' else page.title_en }}</li>
                    </ol>
                </nav>
                <h1 class="h2 mb-0">{{ page.title_zh if get_locale() == 'zh' else page.title_en }}</h1>
            </div>
        </div>
    </div>
</section>

<!-- Page Content -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="page-content">
                    {% if page.content_zh and get_locale() == 'zh' %}
                        {{ page.content_zh | safe }}
                    {% elif page.content_en %}
                        {{ page.content_en | safe }}
                    {% else %}
                        <p class="text-muted text-center py-5">
                            {{ _('Content is not available in the selected language.') }}
                        </p>
                    {% endif %}
                </div>
                
                <!-- Contact CTA -->
                <div class="contact-cta mt-5 p-4 bg-light rounded">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-2">{{ _('Have questions?') }}</h5>
                            <p class="text-muted mb-md-0">{{ _('Our team is here to help you with any inquiries about our products or services.') }}</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a href="{{ url_for('main.contact') }}" class="btn btn-primary">
                                <i class="fas fa-phone me-2"></i>
                                {{ _('Contact Us') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
.page-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--dark-gray);
}

.page-content h1,
.page-content h2,
.page-content h3,
.page-content h4,
.page-content h5,
.page-content h6 {
    color: var(--secondary-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.page-content h1:first-child,
.page-content h2:first-child,
.page-content h3:first-child {
    margin-top: 0;
}

.page-content p {
    margin-bottom: 1.5rem;
}

.page-content ul,
.page-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.page-content li {
    margin-bottom: 0.5rem;
}

.page-content blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: var(--gray);
}

.page-content img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 1.5rem 0;
}

.page-content a {
    color: var(--primary-color);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: var(--transition);
}

.page-content a:hover {
    border-bottom-color: var(--primary-color);
}

.contact-cta {
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

@media (max-width: 768px) {
    .page-content {
        font-size: 1rem;
    }
    
    .contact-cta .row {
        text-align: center;
    }
    
    .contact-cta .btn {
        margin-top: 1rem;
    }
}
</style>
{% endblock %}
