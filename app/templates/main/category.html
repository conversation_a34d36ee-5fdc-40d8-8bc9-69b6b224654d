{% extends "base.html" %}

{% block title %}{{ category.name_zh if get_locale() == 'zh' else category.name_en }} - {{ _('Pecco Pet Portal') }}{% endblock %}

{% block meta_description %}{{ category.description_zh if get_locale() == 'zh' else category.description_en }}{% endblock %}

{% block content %}
<!-- Category Header -->
<section class="category-header bg-light-gray py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">{{ _('Home') }}</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('main.products') }}">{{ _('Products') }}</a></li>
                        <li class="breadcrumb-item active">{{ category.name_zh if get_locale() == 'zh' else category.name_en }}</li>
                    </ol>
                </nav>
                
                <div class="d-flex align-items-center mb-3">
                    {% if category.emoji %}
                    <span class="category-emoji me-3">{{ category.emoji }}</span>
                    {% elif category.icon_image %}
                    <img src="{{ url_for('static', filename='uploads/categories/' + category.icon_image) }}" 
                         alt="{{ category.name_en }}" class="category-icon me-3">
                    {% endif %}
                    <h1 class="h2 mb-0">{{ category.name_zh if get_locale() == 'zh' else category.name_en }}</h1>
                </div>
                
                {% if category.description_zh or category.description_en %}
                <p class="text-muted lead">
                    {{ category.description_zh if get_locale() == 'zh' else category.description_en }}
                </p>
                {% endif %}
            </div>
            <div class="col-md-4 text-md-end">
                <div class="category-stats">
                    <div class="stat-item">
                        <span class="stat-number h4 text-primary">{{ products.total }}</span>
                        <div class="stat-label text-muted">{{ _('Products Available') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Category Content -->
<section class="section-padding">
    <div class="container">
        <!-- Sort and Filter Bar -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="results-info">
                    <span class="text-muted">
                        {{ _('Showing %(start)d-%(end)d of %(total)d products', 
                            start=((products.page - 1) * products.per_page + 1),
                            end=min(products.page * products.per_page, products.total),
                            total=products.total) }}
                    </span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center justify-content-md-end">
                    <label for="sort-select" class="form-label me-2 mb-0">{{ _('Sort by:') }}</label>
                    <select id="sort-select" class="form-select" style="width: auto;" onchange="updateSort(this.value)">
                        <option value="newest" {% if request.args.get('sort') == 'newest' %}selected{% endif %}>{{ _('Newest') }}</option>
                        <option value="price_low" {% if request.args.get('sort') == 'price_low' %}selected{% endif %}>{{ _('Price: Low to High') }}</option>
                        <option value="price_high" {% if request.args.get('sort') == 'price_high' %}selected{% endif %}>{{ _('Price: High to Low') }}</option>
                        <option value="name" {% if request.args.get('sort') == 'name' %}selected{% endif %}>{{ _('Name') }}</option>
                    </select>
                </div>
            </div>
        </div>
        
        {% if products.items %}
        <!-- Products Grid -->
        <div class="row g-4">
            {% for product in products.items %}
            <div class="col-lg-3 col-md-4 col-sm-6">
                {% include 'includes/product_card.html' %}
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if products.pages > 1 %}
        <nav aria-label="Category products pagination" class="mt-5">
            <ul class="pagination justify-content-center">
                {% if products.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('main.category', slug=category.slug, page=products.prev_num, **request.args) }}">
                        <i class="fas fa-chevron-left"></i> {{ _('Previous') }}
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in products.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != products.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.category', slug=category.slug, page=page_num, **request.args) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if products.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('main.category', slug=category.slug, page=products.next_num, **request.args) }}">
                        {{ _('Next') }} <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <!-- No Products Found -->
        <div class="text-center py-5">
            <div class="mb-4">
                {% if category.emoji %}
                <span style="font-size: 4rem;">{{ category.emoji }}</span>
                {% else %}
                <i class="fas fa-box-open fa-4x text-muted"></i>
                {% endif %}
            </div>
            <h3>{{ _('No products in this category yet') }}</h3>
            <p class="text-muted mb-4">{{ _('We\'re working on adding amazing products to this category. Check back soon!') }}</p>
            <a href="{{ url_for('main.products') }}" class="btn btn-primary">
                {{ _('Browse All Products') }}
            </a>
        </div>
        {% endif %}
        
        <!-- Other Categories -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="mb-4">{{ _('Explore Other Categories') }}</h3>
                <div class="row g-3">
                    {% for other_category in get_categories() %}
                        {% if other_category.id != category.id %}
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <a href="{{ url_for('main.category', slug=other_category.slug) }}" 
                               class="category-link d-block text-center p-3 bg-light rounded text-decoration-none">
                                {% if other_category.emoji %}
                                <div class="category-emoji mb-2">{{ other_category.emoji }}</div>
                                {% elif other_category.icon_image %}
                                <img src="{{ url_for('static', filename='uploads/categories/' + other_category.icon_image) }}" 
                                     alt="{{ other_category.name_en }}" class="category-icon-small mb-2">
                                {% else %}
                                <i class="fas fa-paw fa-2x text-primary mb-2"></i>
                                {% endif %}
                                <div class="category-name small fw-semibold">
                                    {{ other_category.name_zh if get_locale() == 'zh' else other_category.name_en }}
                                </div>
                            </a>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="cta-section bg-primary text-white py-5">
    <div class="container text-center">
        <h3 class="mb-3">{{ _('Can\'t find what you\'re looking for?') }}</h3>
        <p class="lead mb-4">{{ _('Our pet experts are here to help you find the perfect products for your beloved companion.') }}</p>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="{{ url_for('main.contact') }}" class="btn btn-light btn-lg">
                <i class="fas fa-phone me-2"></i>
                {{ _('Contact Our Experts') }}
            </a>
            <a href="{{ url_for('main.products') }}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-search me-2"></i>
                {{ _('Browse All Products') }}
            </a>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function updateSort(sortValue) {
    const url = new URL(window.location);
    if (sortValue) {
        url.searchParams.set('sort', sortValue);
    } else {
        url.searchParams.delete('sort');
    }
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}

// Smooth scroll to top when pagination is clicked
document.querySelectorAll('.pagination a').forEach(link => {
    link.addEventListener('click', function() {
        setTimeout(() => {
            window.scrollTo({
                top: document.querySelector('.category-header').offsetTop - 100,
                behavior: 'smooth'
            });
        }, 100);
    });
});
</script>

<style>
.category-emoji {
    font-size: 3rem;
    line-height: 1;
}

.category-icon {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 50%;
}

.category-icon-small {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
}

.category-stats .stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.category-link {
    transition: var(--transition);
    border: 1px solid transparent;
}

.category-link:hover {
    background-color: var(--white) !important;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    color: inherit !important;
}

.category-link .category-emoji {
    font-size: 2rem;
}

.category-link .category-name {
    color: var(--secondary-color);
}

.results-info {
    padding: 0.75rem 0;
}

.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: 1px solid var(--border-color);
    color: var(--secondary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.cta-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

@media (max-width: 768px) {
    .category-header .row {
        text-align: center;
    }
    
    .category-emoji {
        font-size: 2.5rem;
    }
    
    .category-icon {
        width: 50px;
        height: 50px;
    }
    
    .results-info {
        text-align: center;
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}
