{% extends "base.html" %}

{% block title %}{{ _('Products') }} - {{ _('Pecco Pet Portal') }}{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header bg-light-gray py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">{{ _('Home') }}</a></li>
                        <li class="breadcrumb-item active">{{ _('Products') }}</li>
                    </ol>
                </nav>
                <h1 class="h2 mb-0">🐾 {{ _('All Products') }}</h1>
                <p class="text-muted">{{ _('Discover our complete collection of luxury pet supplies') }}</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex align-items-center justify-content-md-end">
                    <label for="sort-select" class="form-label me-2 mb-0">{{ _('Sort by:') }}</label>
                    <select id="sort-select" class="form-select" style="width: auto;" onchange="updateSort(this.value)">
                        <option value="newest" {% if request.args.get('sort') == 'newest' %}selected{% endif %}>{{ _('Newest') }}</option>
                        <option value="price_low" {% if request.args.get('sort') == 'price_low' %}selected{% endif %}>{{ _('Price: Low to High') }}</option>
                        <option value="price_high" {% if request.args.get('sort') == 'price_high' %}selected{% endif %}>{{ _('Price: High to Low') }}</option>
                        <option value="name" {% if request.args.get('sort') == 'name' %}selected{% endif %}>{{ _('Name') }}</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="section-padding">
    <div class="container">
        <div class="row">
            <!-- Sidebar Filters -->
            <div class="col-lg-3 mb-4">
                <div class="filters-sidebar">
                    <!-- Category Filter -->
                    <div class="filter-group mb-4">
                        <h5 class="filter-title">{{ _('Categories') }}</h5>
                        <div class="filter-options">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="category" id="all-categories" 
                                       value="" {% if not request.args.get('category') %}checked{% endif %}
                                       onchange="updateCategory('')">
                                <label class="form-check-label" for="all-categories">
                                    {{ _('All Categories') }}
                                </label>
                            </div>
                            {% for category in get_categories() %}
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="category" 
                                       id="category-{{ category.id }}" value="{{ category.slug }}"
                                       {% if request.args.get('category') == category.slug %}checked{% endif %}
                                       onchange="updateCategory('{{ category.slug }}')">
                                <label class="form-check-label" for="category-{{ category.id }}">
                                    {% if category.emoji %}{{ category.emoji }}{% endif %}
                                    {{ category.name_zh if get_locale() == 'zh' else category.name_en }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Quick Filters -->
                    <div class="filter-group mb-4">
                        <h5 class="filter-title">{{ _('Quick Filters') }}</h5>
                        <div class="filter-tags">
                            <a href="{{ url_for('main.products', featured='true') }}" 
                               class="btn btn-sm btn-outline-primary mb-2 me-2">
                                ✨ {{ _('Featured') }}
                            </a>
                            <a href="{{ url_for('main.products', new='true') }}" 
                               class="btn btn-sm btn-outline-success mb-2 me-2">
                                🆕 {{ _('New') }}
                            </a>
                            <a href="{{ url_for('main.products', hot='true') }}" 
                               class="btn btn-sm btn-outline-warning mb-2 me-2">
                                🔥 {{ _('Hot') }}
                            </a>
                        </div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="contact-card bg-light p-4 rounded">
                        <h6 class="fw-bold mb-3">{{ _('Need Help?') }}</h6>
                        <p class="small text-muted mb-3">{{ _('Our pet experts are here to help you find the perfect products.') }}</p>
                        <div class="contact-info small">
                            <div class="mb-2">
                                <i class="fas fa-phone text-primary me-2"></i>
                                {{ get_setting('contact_phone') }}
                            </div>
                            <div class="mb-2">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                {{ get_setting('contact_email') }}
                            </div>
                        </div>
                        <a href="{{ url_for('main.contact') }}" class="btn btn-primary btn-sm w-100 mt-3">
                            {{ _('Contact Us') }}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="col-lg-9">
                {% if products.items %}
                <!-- Results Info -->
                <div class="results-info d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <span class="text-muted">
                            {{ _('Showing %(start)d-%(end)d of %(total)d products', 
                                start=((products.page - 1) * products.per_page + 1),
                                end=min(products.page * products.per_page, products.total),
                                total=products.total) }}
                        </span>
                    </div>
                    <div class="view-toggle d-none d-md-block">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="setGridView(3)">
                                <i class="fas fa-th"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setGridView(4)">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Products Grid -->
                <div class="row g-4" id="products-grid">
                    {% for product in products.items %}
                    <div class="col-lg-4 col-md-6 product-col">
                        {% include 'includes/product_card.html' %}
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if products.pages > 1 %}
                <nav aria-label="Products pagination" class="mt-5">
                    <ul class="pagination justify-content-center">
                        {% if products.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.products', page=products.prev_num, **request.args) }}">
                                <i class="fas fa-chevron-left"></i> {{ _('Previous') }}
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in products.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != products.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main.products', page=page_num, **request.args) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if products.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.products', page=products.next_num, **request.args) }}">
                                {{ _('Next') }} <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <!-- No Products Found -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-search fa-4x text-muted"></i>
                    </div>
                    <h3>{{ _('No products found') }}</h3>
                    <p class="text-muted mb-4">{{ _('Try adjusting your filters or browse all categories.') }}</p>
                    <a href="{{ url_for('main.products') }}" class="btn btn-primary">
                        {{ _('View All Products') }}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function updateSort(sortValue) {
    const url = new URL(window.location);
    if (sortValue) {
        url.searchParams.set('sort', sortValue);
    } else {
        url.searchParams.delete('sort');
    }
    window.location.href = url.toString();
}

function updateCategory(categorySlug) {
    const url = new URL(window.location);
    if (categorySlug) {
        url.searchParams.set('category', categorySlug);
    } else {
        url.searchParams.delete('category');
    }
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}

function setGridView(columns) {
    const grid = document.getElementById('products-grid');
    const cols = grid.querySelectorAll('.product-col');
    
    // Update button states
    document.querySelectorAll('.view-toggle .btn').forEach(btn => btn.classList.remove('active'));
    event.target.closest('.btn').classList.add('active');
    
    // Update grid columns
    cols.forEach(col => {
        col.className = col.className.replace(/col-lg-\d+/, `col-lg-${12/columns}`);
    });
}

// Smooth scroll to top when pagination is clicked
document.querySelectorAll('.pagination a').forEach(link => {
    link.addEventListener('click', function() {
        setTimeout(() => {
            window.scrollTo({
                top: document.querySelector('.page-header').offsetTop - 100,
                behavior: 'smooth'
            });
        }, 100);
    });
});
</script>

<style>
.filters-sidebar {
    position: sticky;
    top: 120px;
}

.filter-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.filter-options .form-check {
    margin-bottom: 0.75rem;
}

.filter-options .form-check-label {
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-options .form-check-label:hover {
    color: var(--primary-color);
}

.filter-tags .btn {
    border-radius: 20px;
    font-size: 0.85rem;
}

.contact-card {
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.results-info {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.view-toggle .btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: 1px solid var(--border-color);
    color: var(--secondary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

@media (max-width: 768px) {
    .filters-sidebar {
        position: static;
    }
    
    .results-info {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>
{% endblock %}
