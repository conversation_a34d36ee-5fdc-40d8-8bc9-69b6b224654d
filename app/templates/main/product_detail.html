{% extends "base.html" %}

{% block title %}{{ product.title_zh if get_locale() == 'zh' else product.title_en }} - {{ _('Pecco Pet Portal') }}{% endblock %}

{% block meta_description %}{{ product.short_description_zh if get_locale() == 'zh' else product.short_description_en }}{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<section class="breadcrumb-section py-3 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">{{ _('Home') }}</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('main.products') }}">{{ _('Products') }}</a></li>
                {% if product.category %}
                <li class="breadcrumb-item">
                    <a href="{{ url_for('main.category', slug=product.category.slug) }}">
                        {{ product.category.name_zh if get_locale() == 'zh' else product.category.name_en }}
                    </a>
                </li>
                {% endif %}
                <li class="breadcrumb-item active">{{ product.title_zh if get_locale() == 'zh' else product.title_en }}</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Product Detail -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <!-- Product Images -->
            <div class="col-lg-6 mb-4">
                <div class="product-images">
                    <!-- Main Image -->
                    <div class="main-image mb-3">
                        {% if product.main_image %}
                        <img id="main-product-image" 
                             src="{{ url_for('static', filename='uploads/products/' + product.main_image) }}" 
                             alt="{{ product.title_en }}" 
                             class="img-fluid rounded shadow">
                        {% else %}
                        <div class="placeholder-image d-flex align-items-center justify-content-center bg-light rounded" style="height: 400px;">
                            <i class="fas fa-image fa-4x text-muted"></i>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Gallery Thumbnails -->
                    {% if product.gallery_images %}
                    <div class="image-gallery">
                        <div class="row g-2">
                            <!-- Main image thumbnail -->
                            {% if product.main_image %}
                            <div class="col-3">
                                <img src="{{ url_for('static', filename='uploads/products/' + product.main_image) }}" 
                                     alt="{{ product.title_en }}" 
                                     class="img-fluid rounded thumbnail active"
                                     onclick="changeMainImage(this.src)">
                            </div>
                            {% endif %}
                            
                            <!-- Gallery images -->
                            {% set gallery = product.gallery_images | from_json %}
                            {% if gallery %}
                                {% for image in gallery[:3] %}
                                <div class="col-3">
                                    <img src="{{ url_for('static', filename='uploads/products/' + image) }}" 
                                         alt="{{ product.title_en }}" 
                                         class="img-fluid rounded thumbnail"
                                         onclick="changeMainImage(this.src)">
                                </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Product Info -->
            <div class="col-lg-6">
                <div class="product-info">
                    <!-- Product Badges -->
                    <div class="product-badges mb-3">
                        {% if product.is_new %}
                        <span class="badge bg-success me-2">{{ _('New') }}</span>
                        {% endif %}
                        {% if product.is_hot %}
                        <span class="badge bg-warning me-2">{{ _('Hot') }}</span>
                        {% endif %}
                        {% if product.is_featured %}
                        <span class="badge bg-primary me-2">{{ _('Featured') }}</span>
                        {% endif %}
                    </div>
                    
                    <!-- Vendor -->
                    {% if product.vendor %}
                    <div class="product-vendor text-muted mb-2">
                        <small>{{ _('By') }} {{ product.vendor }}</small>
                    </div>
                    {% endif %}
                    
                    <!-- Title -->
                    <h1 class="product-title h2 mb-3">
                        {{ product.title_zh if get_locale() == 'zh' else product.title_en }}
                    </h1>
                    
                    <!-- Price -->
                    {% if product.price %}
                    <div class="product-price mb-4">
                        <span class="current-price h3 text-primary fw-bold">
                            ${{ "%.2f" | format(product.price) }}
                        </span>
                        {% if product.compare_price and product.compare_price > product.price %}
                        <span class="compare-price text-muted text-decoration-line-through ms-2">
                            ${{ "%.2f" | format(product.compare_price) }}
                        </span>
                        <span class="discount-badge badge bg-danger ms-2">
                            -{{ ((product.compare_price - product.price) / product.compare_price * 100) | round | int }}%
                        </span>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <!-- Short Description -->
                    {% if product.short_description_zh or product.short_description_en %}
                    <div class="product-short-description mb-4">
                        <p class="lead">
                            {{ product.short_description_zh if get_locale() == 'zh' else product.short_description_en }}
                        </p>
                    </div>
                    {% endif %}
                    
                    <!-- SKU -->
                    {% if product.sku %}
                    <div class="product-sku mb-3">
                        <small class="text-muted">{{ _('SKU:') }} {{ product.sku }}</small>
                    </div>
                    {% endif %}
                    
                    <!-- Action Buttons -->
                    <div class="product-actions mb-4">
                        <div class="d-grid gap-2 d-md-block">
                            <button class="btn btn-primary btn-lg me-md-2" onclick="showContactModal()">
                                <i class="fas fa-phone me-2"></i>
                                {{ _('Contact for Details') }}
                            </button>
                            <button class="btn btn-outline-secondary btn-lg" onclick="shareProduct()">
                                <i class="fas fa-share-alt me-2"></i>
                                {{ _('Share') }}
                            </button>
                        </div>
                    </div>
                    
                    <!-- Product Features -->
                    <div class="product-features">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="feature-item text-center p-3 bg-light rounded">
                                    <i class="fas fa-award text-primary mb-2"></i>
                                    <div class="small">{{ _('Premium Quality') }}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-item text-center p-3 bg-light rounded">
                                    <i class="fas fa-shipping-fast text-primary mb-2"></i>
                                    <div class="small">{{ _('Fast Shipping') }}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-item text-center p-3 bg-light rounded">
                                    <i class="fas fa-shield-alt text-primary mb-2"></i>
                                    <div class="small">{{ _('Safe & Secure') }}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-item text-center p-3 bg-light rounded">
                                    <i class="fas fa-heart text-primary mb-2"></i>
                                    <div class="small">{{ _('Pet Approved') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Product Details Tabs -->
        <div class="row mt-5">
            <div class="col-12">
                <ul class="nav nav-tabs" id="productTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="description-tab" data-bs-toggle="tab" 
                                data-bs-target="#description" type="button" role="tab">
                            {{ _('Description') }}
                        </button>
                    </li>
                    {% if reviews %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" 
                                data-bs-target="#reviews" type="button" role="tab">
                            {{ _('Reviews') }} ({{ reviews | length }})
                        </button>
                    </li>
                    {% endif %}
                </ul>
                
                <div class="tab-content" id="productTabsContent">
                    <!-- Description Tab -->
                    <div class="tab-pane fade show active" id="description" role="tabpanel">
                        <div class="py-4">
                            {% if product.description_zh or product.description_en %}
                            <div class="product-description">
                                {{ (product.description_zh if get_locale() == 'zh' else product.description_en) | safe }}
                            </div>
                            {% else %}
                            <p class="text-muted">{{ _('No detailed description available.') }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Reviews Tab -->
                    {% if reviews %}
                    <div class="tab-pane fade" id="reviews" role="tabpanel">
                        <div class="py-4">
                            <div class="row">
                                {% for review in reviews %}
                                <div class="col-md-6 mb-4">
                                    <div class="review-card card h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                {% if review.customer_avatar %}
                                                <img src="{{ url_for('static', filename='uploads/reviews/' + review.customer_avatar) }}" 
                                                     alt="{{ review.customer_name }}" class="rounded-circle me-3" width="40" height="40">
                                                {% else %}
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                                {% endif %}
                                                <div>
                                                    <h6 class="mb-0">{{ review.customer_name }}</h6>
                                                    <div class="text-warning">
                                                        {% for i in range(review.rating) %}
                                                        <i class="fas fa-star"></i>
                                                        {% endfor %}
                                                        {% for i in range(5 - review.rating) %}
                                                        <i class="far fa-star"></i>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            {% if review.title_zh or review.title_en %}
                                            <h6 class="card-title">
                                                {{ review.title_zh if get_locale() == 'zh' else review.title_en }}
                                            </h6>
                                            {% endif %}
                                            
                                            <p class="card-text">
                                                {{ review.content_zh if get_locale() == 'zh' else review.content_en }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Related Products -->
        {% if related_products %}
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="mb-4">{{ _('Related Products') }}</h3>
                <div class="row g-4">
                    {% for product in related_products %}
                    <div class="col-lg-3 col-md-6">
                        {% include 'includes/product_card.html' %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</section>

<!-- Contact Modal -->
<div class="modal fade" id="contactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ _('Contact Us About This Product') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="contact-info">
                    <div class="mb-3">
                        <strong>{{ _('Phone:') }}</strong> {{ get_setting('contact_phone') }}
                    </div>
                    <div class="mb-3">
                        <strong>{{ _('Email:') }}</strong> {{ get_setting('contact_email') }}
                    </div>
                    <div class="mb-3">
                        <strong>{{ _('Product:') }}</strong> {{ product.title_zh if get_locale() == 'zh' else product.title_en }}
                    </div>
                    {% if product.sku %}
                    <div class="mb-3">
                        <strong>{{ _('SKU:') }}</strong> {{ product.sku }}
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="modal-footer">
                <a href="{{ url_for('main.contact') }}" class="btn btn-primary">
                    {{ _('Go to Contact Page') }}
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ _('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Structured Data for SEO -->
<script type="application/ld+json">
{{ get_structured_data_for_product(product) | tojson }}
</script>

<script>
function changeMainImage(src) {
    document.getElementById('main-product-image').src = src;
    
    // Update thumbnail active state
    document.querySelectorAll('.thumbnail').forEach(thumb => {
        thumb.classList.remove('active');
    });
    event.target.classList.add('active');
}

function showContactModal() {
    const modal = new bootstrap.Modal(document.getElementById('contactModal'));
    modal.show();
}

function shareProduct() {
    if (navigator.share) {
        navigator.share({
            title: '{{ product.title_en }}',
            text: '{{ product.short_description_en }}',
            url: window.location.href
        });
    } else {
        // Fallback: copy URL to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            PeccoApp.showNotification('{{ _("Product URL copied to clipboard!") }}', 'success');
        });
    }
}

// Image zoom on hover
document.getElementById('main-product-image').addEventListener('mousemove', function(e) {
    const rect = this.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    this.style.transformOrigin = `${x}px ${y}px`;
    this.style.transform = 'scale(1.5)';
});

document.getElementById('main-product-image').addEventListener('mouseleave', function() {
    this.style.transform = 'scale(1)';
});
</script>

<style>
.thumbnail {
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
    border: 2px solid transparent;
}

.thumbnail:hover,
.thumbnail.active {
    opacity: 1;
    border-color: var(--primary-color);
}

.product-badges .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

.product-price .current-price {
    font-size: 2rem;
}

.product-features .feature-item {
    transition: var(--transition);
}

.product-features .feature-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--gray);
    font-weight: 600;
}

.nav-tabs .nav-link.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    background: none;
}

.review-card {
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.review-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

#main-product-image {
    transition: transform 0.3s ease;
    cursor: zoom-in;
}

@media (max-width: 768px) {
    .product-price .current-price {
        font-size: 1.5rem;
    }
    
    .product-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
{% endblock %}
