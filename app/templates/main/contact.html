{% extends "base.html" %}

{% block title %}{{ _('Contact Us') }} - {{ _('Pecco Pet Portal') }}{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header bg-light-gray py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-center">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">{{ _('Home') }}</a></li>
                        <li class="breadcrumb-item active">{{ _('Contact Us') }}</li>
                    </ol>
                </nav>
                <h1 class="h2 mb-3">📞 {{ _('Contact Us') }}</h1>
                <p class="lead text-muted">{{ _('We\'d love to hear from you! Get in touch with our pet experts.') }}</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <!-- Contact Information -->
            <div class="col-lg-4 mb-5">
                <div class="contact-info">
                    <h3 class="mb-4">{{ _('Get in Touch') }}</h3>
                    <p class="text-muted mb-4">
                        {{ _('Our friendly team is here to help you find the perfect products for your beloved pets.') }}
                    </p>
                    
                    <!-- Contact Details -->
                    <div class="contact-details">
                        <div class="contact-item d-flex align-items-start mb-4">
                            <div class="contact-icon me-3">
                                <i class="fas fa-map-marker-alt fa-lg text-primary"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">{{ _('Address') }}</h6>
                                <p class="text-muted mb-0">
                                    123 Pet Paradise Street<br>
                                    Happy Pets District<br>
                                    Pet City, PC 12345
                                </p>
                            </div>
                        </div>
                        
                        <div class="contact-item d-flex align-items-start mb-4">
                            <div class="contact-icon me-3">
                                <i class="fas fa-phone fa-lg text-primary"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">{{ _('Phone') }}</h6>
                                <p class="text-muted mb-0">
                                    <a href="tel:{{ get_setting('contact_phone') }}" class="text-decoration-none">
                                        {{ get_setting('contact_phone') }}
                                    </a>
                                </p>
                            </div>
                        </div>
                        
                        <div class="contact-item d-flex align-items-start mb-4">
                            <div class="contact-icon me-3">
                                <i class="fas fa-envelope fa-lg text-primary"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">{{ _('Email') }}</h6>
                                <p class="text-muted mb-0">
                                    <a href="mailto:{{ get_setting('contact_email') }}" class="text-decoration-none">
                                        {{ get_setting('contact_email') }}
                                    </a>
                                </p>
                            </div>
                        </div>
                        
                        <div class="contact-item d-flex align-items-start mb-4">
                            <div class="contact-icon me-3">
                                <i class="fas fa-clock fa-lg text-primary"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">{{ _('Business Hours') }}</h6>
                                <p class="text-muted mb-0">
                                    {{ _('Monday - Friday: 9:00 AM - 6:00 PM') }}<br>
                                    {{ _('Saturday: 10:00 AM - 4:00 PM') }}<br>
                                    {{ _('Sunday: Closed') }}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Social Media -->
                    <div class="social-media mt-4">
                        <h6 class="fw-bold mb-3">{{ _('Follow Us') }}</h6>
                        <div class="social-links">
                            <a href="#" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-info btn-sm me-2 mb-2">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="btn btn-outline-danger btn-sm me-2 mb-2">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="btn btn-outline-success btn-sm me-2 mb-2">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="contact-form">
                    <div class="card shadow-sm">
                        <div class="card-body p-4">
                            <h3 class="card-title mb-4">{{ _('Send us a Message') }}</h3>
                            
                            <form id="contact-form" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">{{ _('Your Name') }} *</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                        <div class="invalid-feedback">
                                            {{ _('Please provide your name.') }}
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">{{ _('Email Address') }} *</label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                        <div class="invalid-feedback">
                                            {{ _('Please provide a valid email address.') }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">{{ _('Phone Number') }}</label>
                                        <input type="tel" class="form-control" id="phone" name="phone">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="subject" class="form-label">{{ _('Subject') }} *</label>
                                        <select class="form-select" id="subject" name="subject" required>
                                            <option value="">{{ _('Choose a subject...') }}</option>
                                            <option value="product-inquiry">{{ _('Product Inquiry') }}</option>
                                            <option value="order-support">{{ _('Order Support') }}</option>
                                            <option value="general-question">{{ _('General Question') }}</option>
                                            <option value="feedback">{{ _('Feedback') }}</option>
                                            <option value="partnership">{{ _('Partnership') }}</option>
                                            <option value="other">{{ _('Other') }}</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            {{ _('Please select a subject.') }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="pet-type" class="form-label">{{ _('Pet Type') }}</label>
                                    <select class="form-select" id="pet-type" name="pet_type">
                                        <option value="">{{ _('Select your pet type...') }}</option>
                                        <option value="dog">🐶 {{ _('Dog') }}</option>
                                        <option value="cat">🐱 {{ _('Cat') }}</option>
                                        <option value="bird">🐦 {{ _('Bird') }}</option>
                                        <option value="fish">🐠 {{ _('Fish') }}</option>
                                        <option value="small-pet">🐹 {{ _('Small Pet') }}</option>
                                        <option value="reptile">🦎 {{ _('Reptile') }}</option>
                                        <option value="other">🐾 {{ _('Other') }}</option>
                                    </select>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="message" class="form-label">{{ _('Message') }} *</label>
                                    <textarea class="form-control" id="message" name="message" rows="6" 
                                              placeholder="{{ _('Tell us how we can help you and your pet...') }}" required></textarea>
                                    <div class="invalid-feedback">
                                        {{ _('Please provide your message.') }}
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                                        <label class="form-check-label" for="newsletter">
                                            {{ _('Subscribe to our newsletter for pet care tips and product updates') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        {{ _('Send Message') }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section bg-light-gray py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title text-center mb-5">
                    <h2>❓ {{ _('Frequently Asked Questions') }}</h2>
                    <p>{{ _('Quick answers to common questions about our products and services') }}</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                {{ _('What makes Pecco products different?') }}
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {{ _('Our products are carefully curated for quality and designed with both pets and owners in mind. We focus on luxury materials, innovative designs, and safety standards that exceed industry requirements.') }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                {{ _('Do you offer international shipping?') }}
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {{ _('Currently, we ship to select countries. Please contact us with your location, and we\'ll let you know if we can deliver to your area.') }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                {{ _('How can I choose the right product for my pet?') }}
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {{ _('Our pet experts are here to help! Contact us with information about your pet\'s size, age, and preferences, and we\'ll recommend the perfect products.') }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                {{ _('What is your return policy?') }}
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {{ _('We want you and your pet to be completely satisfied. If you\'re not happy with your purchase, please contact us within 30 days for return or exchange options.') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Contact form handling
document.getElementById('contact-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (this.checkValidity()) {
        // Show loading
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{{ _("Sending...") }}';
        submitBtn.disabled = true;
        
        // Simulate form submission (replace with actual API call)
        setTimeout(() => {
            PeccoApp.showNotification('{{ _("Thank you for your message! We\'ll get back to you soon.") }}', 'success');
            this.reset();
            this.classList.remove('was-validated');
            
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }
    
    this.classList.add('was-validated');
});

// Smooth scroll to form when coming from product pages
if (window.location.hash === '#contact-form') {
    setTimeout(() => {
        document.getElementById('contact-form').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }, 500);
}
</script>

<style>
.contact-icon {
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-item {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.contact-item:last-child {
    border-bottom: none;
}

.social-links .btn {
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.contact-form .card {
    border: none;
    border-radius: var(--border-radius-lg);
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(225, 115, 44, 0.25);
}

.accordion-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.accordion-button {
    background: var(--white);
    border: none;
    font-weight: 600;
    color: var(--secondary-color);
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: var(--white);
}

.accordion-button:focus {
    box-shadow: none;
}

@media (max-width: 768px) {
    .contact-info {
        text-align: center;
    }
    
    .contact-item {
        justify-content: center;
        text-align: center;
    }
}
</style>
{% endblock %}
