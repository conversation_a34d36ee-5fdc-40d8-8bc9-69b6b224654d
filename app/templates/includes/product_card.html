<!-- Product Card Component -->
<div class="product-card animate-on-scroll">
    <div class="product-image">
        {% if product.main_image %}
        <img src="{{ url_for('static', filename='uploads/products/' + product.main_image) }}" 
             alt="{{ product.title_en }}" 
             class="w-100 h-100 object-fit-cover"
             loading="lazy">
        {% else %}
        <div class="d-flex align-items-center justify-content-center bg-light h-100">
            <i class="fas fa-image text-muted fa-3x"></i>
        </div>
        {% endif %}
        
        <!-- Product Badges -->
        <div class="product-badges">
            {% if product.is_new %}
            <span class="product-badge new">{{ _('New') }}</span>
            {% endif %}
            {% if product.is_hot %}
            <span class="product-badge hot">{{ _('Hot') }}</span>
            {% endif %}
            {% if product.is_featured %}
            <span class="product-badge featured">{{ _('Featured') }}</span>
            {% endif %}
            {% if product.compare_price and product.price < product.compare_price %}
            <span class="product-badge sale">
                {{ _('Sale') }} -{{ ((product.compare_price - product.price) / product.compare_price * 100) | round | int }}%
            </span>
            {% endif %}
        </div>
        
        <!-- Quick View Overlay -->
        <div class="product-overlay">
            <a href="{{ url_for('main.product_detail', slug=product.slug) }}" 
               class="btn btn-primary btn-sm">
                <i class="fas fa-eye me-1"></i>
                {{ _('Quick View') }}
            </a>
        </div>
    </div>
    
    <div class="product-info">
        {% if product.vendor %}
        <div class="product-vendor">{{ product.vendor }}</div>
        {% endif %}
        
        <h5 class="product-title">
            <a href="{{ url_for('main.product_detail', slug=product.slug) }}" class="text-decoration-none">
                {{ product.title_zh if get_locale() == 'zh' else product.title_en }}
            </a>
        </h5>
        
        {% if product.short_description_zh or product.short_description_en %}
        <p class="product-description">
            {{ (product.short_description_zh if get_locale() == 'zh' else product.short_description_en) | truncate(80) }}
        </p>
        {% endif %}
        
        {% if product.price %}
        <div class="product-price">
            ${{ "%.2f" | format(product.price) }}
            {% if product.compare_price and product.compare_price > product.price %}
            <span class="compare-price">${{ "%.2f" | format(product.compare_price) }}</span>
            {% endif %}
        </div>
        {% endif %}
        
        <!-- Product Actions -->
        <div class="product-actions mt-3">
            <a href="{{ url_for('main.product_detail', slug=product.slug) }}" 
               class="btn btn-outline-primary btn-sm w-100">
                <i class="fas fa-info-circle me-1"></i>
                {{ _('View Details') }}
            </a>
        </div>
    </div>
</div>

<style>
/* Product Card Specific Styles */
.product-card {
    position: relative;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badges {
    position: absolute;
    top: 12px;
    left: 12px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.product-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-badge.new {
    background: var(--accent-mint);
    color: var(--dark-gray);
}

.product-badge.hot {
    background: var(--primary-color);
    color: var(--white);
}

.product-badge.featured {
    background: var(--accent-gold);
    color: var(--dark-gray);
}

.product-badge.sale {
    background: #e74c3c;
    color: var(--white);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-info {
    padding: 1.25rem;
}

.product-vendor {
    font-size: 0.75rem;
    color: var(--gray);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.product-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.product-title a {
    color: var(--secondary-color);
    transition: var(--transition);
}

.product-title a:hover {
    color: var(--primary-color);
}

.product-description {
    font-size: 0.875rem;
    color: var(--gray);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.product-price {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.product-price .compare-price {
    font-size: 0.875rem;
    color: var(--gray);
    text-decoration: line-through;
    margin-left: 0.5rem;
    font-weight: 400;
}

.product-actions .btn {
    transition: var(--transition);
}

.product-actions .btn:hover {
    transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .product-image {
        height: 200px;
    }
    
    .product-info {
        padding: 1rem;
    }
    
    .product-title {
        font-size: 0.9rem;
    }
    
    .product-price {
        font-size: 1rem;
    }
}
</style>
