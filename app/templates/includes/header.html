<!-- Top Bar (Optional) -->
<div class="top-bar d-none d-lg-block">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="top-bar-left">
                    <span class="text-muted small">
                        <i class="fas fa-envelope me-1"></i>
                        {{ get_setting('contact_email') }}
                    </span>
                    <span class="text-muted small ms-3">
                        <i class="fas fa-phone me-1"></i>
                        {{ get_setting('contact_phone') }}
                    </span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="top-bar-right text-end">
                    <!-- Language Switcher -->
                    <div class="language-switcher d-inline-block">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                {% if get_locale() == 'zh' %}
                                    <i class="fas fa-globe me-1"></i>中文
                                {% else %}
                                    <i class="fas fa-globe me-1"></i>English
                                {% endif %}
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for(request.endpoint, lang='en', **request.view_args) }}">English</a></li>
                                <li><a class="dropdown-item" href="{{ url_for(request.endpoint, lang='zh', **request.view_args) }}">中文</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Navigation -->
<nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
    <div class="container">
        <!-- Logo and Brand -->
        <a class="navbar-brand d-flex align-items-center" href="{{ url_for('main.index') }}">
            <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Pecco" height="40" class="me-2">
            <div class="brand-text">
                <div class="brand-name fw-bold">Pecco</div>
                <div class="brand-slogan small text-muted">{{ get_setting('brand_slogan') }}</div>
            </div>
        </a>
        
        <!-- Mobile Menu Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- Navigation Menu -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav mx-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-semibold" href="#" data-bs-toggle="dropdown">
                        🐾 {{ _('Products') }}
                    </a>
                    <ul class="dropdown-menu">
                        {% for category in get_categories() %}
                        <li>
                            <a class="dropdown-item d-flex align-items-center" href="{{ url_for('main.category', slug=category.slug) }}">
                                {% if category.emoji %}
                                    <span class="me-2">{{ category.emoji }}</span>
                                {% endif %}
                                {{ category.name_zh if get_locale() == 'zh' else category.name_en }}
                            </a>
                        </li>
                        {% endfor %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item fw-semibold" href="{{ url_for('main.products') }}">{{ _('View All Products') }}</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold" href="{{ url_for('main.page', slug='brand-story') }}">
                        ✨ {{ _('Brand Story') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold" href="{{ url_for('main.page', slug='about-us') }}">
                        🏠 {{ _('About Us') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold" href="{{ url_for('main.news') }}">
                        📰 {{ _('News') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold" href="{{ url_for('main.contact') }}">
                        📞 {{ _('Contact') }}
                    </a>
                </li>
            </ul>
            
            <!-- Right Side Actions -->
            <div class="navbar-nav">
                <!-- Language Switcher (Mobile) -->
                <div class="nav-item dropdown d-lg-none">
                    <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                        <i class="fas fa-globe"></i>
                        {% if get_locale() == 'zh' %}中文{% else %}English{% endif %}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for(request.endpoint, lang='en', **request.view_args) }}">English</a></li>
                        <li><a class="dropdown-item" href="{{ url_for(request.endpoint, lang='zh', **request.view_args) }}">中文</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>
