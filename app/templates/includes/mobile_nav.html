<!-- Mobile Bottom Navigation (Optional) -->
<nav class="mobile-bottom-nav d-lg-none">
    <div class="mobile-nav-container">
        <a href="{{ url_for('main.index') }}" class="mobile-nav-item {% if request.endpoint == 'main.index' %}active{% endif %}">
            <i class="fas fa-home"></i>
            <span>{{ _('Home') }}</span>
        </a>
        
        <a href="{{ url_for('main.products') }}" class="mobile-nav-item {% if request.endpoint == 'main.products' %}active{% endif %}">
            <i class="fas fa-th-large"></i>
            <span>{{ _('Products') }}</span>
        </a>
        
        <div class="mobile-nav-item mobile-nav-categories" onclick="toggleMobileCategories()">
            <i class="fas fa-paw"></i>
            <span>{{ _('Categories') }}</span>
        </div>
        
        <a href="{{ url_for('main.contact') }}" class="mobile-nav-item {% if request.endpoint == 'main.contact' %}active{% endif %}">
            <i class="fas fa-phone"></i>
            <span>{{ _('Contact') }}</span>
        </a>
        
        <div class="mobile-nav-item mobile-nav-menu" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
            <span>{{ _('Menu') }}</span>
        </div>
    </div>
</nav>

<!-- Mobile Categories Overlay -->
<div id="mobile-categories-overlay" class="mobile-overlay">
    <div class="mobile-overlay-content">
        <div class="mobile-overlay-header">
            <h5>🐾 {{ _('Pet Categories') }}</h5>
            <button class="mobile-overlay-close" onclick="closeMobileCategories()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mobile-overlay-body">
            <div class="mobile-categories-grid">
                {% for category in get_categories() %}
                <a href="{{ url_for('main.category', slug=category.slug) }}" class="mobile-category-item">
                    <div class="mobile-category-icon">
                        {% if category.emoji %}
                            {{ category.emoji }}
                        {% else %}
                            🐾
                        {% endif %}
                    </div>
                    <div class="mobile-category-name">
                        {{ category.name_zh if get_locale() == 'zh' else category.name_en }}
                    </div>
                </a>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Mobile Menu Overlay -->
<div id="mobile-menu-overlay" class="mobile-overlay">
    <div class="mobile-overlay-content">
        <div class="mobile-overlay-header">
            <h5>📱 {{ _('Menu') }}</h5>
            <button class="mobile-overlay-close" onclick="closeMobileMenu()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mobile-overlay-body">
            <div class="mobile-menu-items">
                <a href="{{ url_for('main.index') }}" class="mobile-menu-item">
                    <i class="fas fa-home"></i>
                    <span>{{ _('Home') }}</span>
                </a>
                
                <a href="{{ url_for('main.products') }}" class="mobile-menu-item">
                    <i class="fas fa-box"></i>
                    <span>{{ _('All Products') }}</span>
                </a>
                
                <a href="{{ url_for('main.page', slug='brand-story') }}" class="mobile-menu-item">
                    <i class="fas fa-star"></i>
                    <span>{{ _('Brand Story') }}</span>
                </a>
                
                <a href="{{ url_for('main.page', slug='about-us') }}" class="mobile-menu-item">
                    <i class="fas fa-info-circle"></i>
                    <span>{{ _('About Us') }}</span>
                </a>
                
                <a href="{{ url_for('main.news') }}" class="mobile-menu-item">
                    <i class="fas fa-newspaper"></i>
                    <span>{{ _('News') }}</span>
                </a>
                
                <a href="{{ url_for('main.contact') }}" class="mobile-menu-item">
                    <i class="fas fa-phone"></i>
                    <span>{{ _('Contact Us') }}</span>
                </a>
                
                <div class="mobile-menu-divider"></div>
                
                <div class="mobile-menu-item mobile-language-switcher">
                    <i class="fas fa-globe"></i>
                    <span>{{ _('Language') }}</span>
                    <div class="mobile-language-options">
                        <a href="{{ url_for(request.endpoint, lang='en', **request.view_args) }}" 
                           class="mobile-language-option {% if get_locale() == 'en' %}active{% endif %}">
                            🇺🇸 English
                        </a>
                        <a href="{{ url_for(request.endpoint, lang='zh', **request.view_args) }}" 
                           class="mobile-language-option {% if get_locale() == 'zh' %}active{% endif %}">
                            🇨🇳 中文
                        </a>
                    </div>
                </div>
                
                <div class="mobile-menu-divider"></div>
                
                <div class="mobile-contact-info">
                    <div class="mobile-contact-item">
                        <i class="fas fa-phone text-primary"></i>
                        <a href="tel:{{ get_setting('contact_phone') }}">{{ get_setting('contact_phone') }}</a>
                    </div>
                    <div class="mobile-contact-item">
                        <i class="fas fa-envelope text-primary"></i>
                        <a href="mailto:{{ get_setting('contact_email') }}">{{ get_setting('contact_email') }}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Mobile Bottom Navigation */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--white);
    border-top: 1px solid var(--border-color);
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    padding: env(safe-area-inset-bottom) 0 0;
}

.mobile-nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 8px 0;
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--gray);
    font-size: 0.75rem;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
    min-width: 60px;
}

.mobile-nav-item i {
    font-size: 1.2rem;
    margin-bottom: 4px;
}

.mobile-nav-item.active,
.mobile-nav-item:hover {
    color: var(--primary-color);
    background: rgba(225, 115, 44, 0.1);
}

/* Mobile Overlays */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-overlay.show {
    display: flex;
    opacity: 1;
}

.mobile-overlay-content {
    background: var(--white);
    margin: auto;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transform: translateY(50px);
    transition: transform 0.3s ease;
}

.mobile-overlay.show .mobile-overlay-content {
    transform: translateY(0);
}

.mobile-overlay-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--light-gray);
}

.mobile-overlay-header h5 {
    margin: 0;
    color: var(--secondary-color);
}

.mobile-overlay-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--gray);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-overlay-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

.mobile-overlay-body {
    padding: 1.25rem;
    max-height: calc(80vh - 80px);
    overflow-y: auto;
}

/* Mobile Categories Grid */
.mobile-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
}

.mobile-category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--secondary-color);
    padding: 1rem 0.5rem;
    border-radius: var(--border-radius);
    background: var(--light-gray);
    transition: var(--transition);
}

.mobile-category-item:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.mobile-category-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.mobile-category-name {
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    line-height: 1.2;
}

/* Mobile Menu Items */
.mobile-menu-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mobile-menu-item {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--secondary-color);
    padding: 12px 16px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.mobile-menu-item:hover {
    background: var(--light-gray);
    color: var(--primary-color);
}

.mobile-menu-item i {
    width: 24px;
    margin-right: 12px;
    text-align: center;
}

.mobile-menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: 0.5rem 0;
}

/* Mobile Language Switcher */
.mobile-language-switcher {
    flex-direction: column;
    align-items: flex-start;
}

.mobile-language-options {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    width: 100%;
}

.mobile-language-option {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--gray);
    font-size: 0.85rem;
    transition: var(--transition);
}

.mobile-language-option.active,
.mobile-language-option:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* Mobile Contact Info */
.mobile-contact-info {
    margin-top: 1rem;
}

.mobile-contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.mobile-contact-item i {
    width: 20px;
    margin-right: 8px;
}

.mobile-contact-item a {
    color: var(--secondary-color);
    text-decoration: none;
}

.mobile-contact-item a:hover {
    color: var(--primary-color);
}

/* Add bottom padding to body when mobile nav is present */
@media (max-width: 991px) {
    body {
        padding-bottom: 70px;
    }
}
</style>

<script>
function toggleMobileCategories() {
    const overlay = document.getElementById('mobile-categories-overlay');
    overlay.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function closeMobileCategories() {
    const overlay = document.getElementById('mobile-categories-overlay');
    overlay.classList.remove('show');
    document.body.style.overflow = '';
}

function toggleMobileMenu() {
    const overlay = document.getElementById('mobile-menu-overlay');
    overlay.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function closeMobileMenu() {
    const overlay = document.getElementById('mobile-menu-overlay');
    overlay.classList.remove('show');
    document.body.style.overflow = '';
}

// Close overlays when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('mobile-overlay')) {
        closeMobileCategories();
        closeMobileMenu();
    }
});

// Close overlays on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeMobileCategories();
        closeMobileMenu();
    }
});
</script>
