"""
SEO优化工具
包含sitemap生成、meta标签优化等功能
"""

from flask import url_for, current_app
from datetime import datetime
from app.models import Product, Category, Page, NewsPost
import xml.etree.ElementTree as ET

def generate_sitemap():
    """生成XML sitemap"""
    
    # 创建根元素
    urlset = ET.Element('urlset')
    urlset.set('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9')
    urlset.set('xmlns:xhtml', 'http://www.w3.org/1999/xhtml')
    
    # 添加首页
    add_url_to_sitemap(urlset, 'main.index', priority='1.0', changefreq='daily')
    
    # 添加主要页面
    main_pages = [
        ('main.products', '0.9', 'daily'),
        ('main.contact', '0.7', 'monthly'),
        ('main.news', '0.8', 'weekly')
    ]
    
    for endpoint, priority, changefreq in main_pages:
        add_url_to_sitemap(urlset, endpoint, priority=priority, changefreq=changefreq)
    
    # 添加分类页面
    categories = Category.query.filter_by(is_active=True).all()
    for category in categories:
        add_url_to_sitemap(
            urlset, 
            'main.category', 
            slug=category.slug,
            priority='0.8',
            changefreq='weekly',
            lastmod=category.updated_at
        )
    
    # 添加产品页面
    products = Product.query.filter_by(is_active=True).all()
    for product in products:
        add_url_to_sitemap(
            urlset,
            'main.product_detail',
            slug=product.slug,
            priority='0.7',
            changefreq='monthly',
            lastmod=product.updated_at
        )
    
    # 添加静态页面
    pages = Page.query.filter_by(is_active=True).all()
    for page in pages:
        add_url_to_sitemap(
            urlset,
            'main.page',
            slug=page.slug,
            priority='0.6',
            changefreq='monthly',
            lastmod=page.updated_at
        )
    
    # 添加新闻页面
    news_posts = NewsPost.query.filter_by(is_published=True).all()
    for post in news_posts:
        add_url_to_sitemap(
            urlset,
            'main.news_detail',
            slug=post.slug,
            priority='0.6',
            changefreq='yearly',
            lastmod=post.updated_at
        )
    
    # 生成XML字符串
    tree = ET.ElementTree(urlset)
    ET.indent(tree, space="  ", level=0)
    
    return ET.tostring(urlset, encoding='unicode', xml_declaration=True)

def add_url_to_sitemap(urlset, endpoint, priority='0.5', changefreq='monthly', lastmod=None, **kwargs):
    """添加URL到sitemap"""
    
    url_elem = ET.SubElement(urlset, 'url')
    
    # 添加URL (支持多语言)
    loc_en = ET.SubElement(url_elem, 'loc')
    loc_en.text = url_for(endpoint, _external=True, lang='en', **kwargs)
    
    # 添加中文版本的链接
    xhtml_link_zh = ET.SubElement(url_elem, 'xhtml:link')
    xhtml_link_zh.set('rel', 'alternate')
    xhtml_link_zh.set('hreflang', 'zh')
    xhtml_link_zh.set('href', url_for(endpoint, _external=True, lang='zh', **kwargs))
    
    # 添加英文版本的链接
    xhtml_link_en = ET.SubElement(url_elem, 'xhtml:link')
    xhtml_link_en.set('rel', 'alternate')
    xhtml_link_en.set('hreflang', 'en')
    xhtml_link_en.set('href', url_for(endpoint, _external=True, lang='en', **kwargs))
    
    # 添加默认语言链接
    xhtml_link_default = ET.SubElement(url_elem, 'xhtml:link')
    xhtml_link_default.set('rel', 'alternate')
    xhtml_link_default.set('hreflang', 'x-default')
    xhtml_link_default.set('href', url_for(endpoint, _external=True, **kwargs))
    
    # 添加最后修改时间
    if lastmod:
        lastmod_elem = ET.SubElement(url_elem, 'lastmod')
        if isinstance(lastmod, datetime):
            lastmod_elem.text = lastmod.strftime('%Y-%m-%d')
        else:
            lastmod_elem.text = str(lastmod)
    
    # 添加更新频率
    changefreq_elem = ET.SubElement(url_elem, 'changefreq')
    changefreq_elem.text = changefreq
    
    # 添加优先级
    priority_elem = ET.SubElement(url_elem, 'priority')
    priority_elem.text = priority

def generate_robots_txt():
    """生成robots.txt"""
    
    robots_content = f"""User-agent: *
Allow: /

# Sitemaps
Sitemap: {url_for('main.sitemap', _external=True)}

# Disallow admin areas
Disallow: /admin/
Disallow: /api/

# Disallow upload directories (if not needed for SEO)
Disallow: /static/uploads/

# Allow important static files
Allow: /static/css/
Allow: /static/js/
Allow: /static/images/

# Crawl delay (optional)
Crawl-delay: 1
"""
    
    return robots_content

def get_structured_data_for_product(product):
    """为产品生成结构化数据 (JSON-LD)"""
    
    structured_data = {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": product.title_en,
        "description": product.short_description_en or product.description_en,
        "brand": {
            "@type": "Brand",
            "name": product.vendor or "Pecco"
        },
        "category": product.category.name_en if product.category else "Pet Supplies",
        "url": url_for('main.product_detail', slug=product.slug, _external=True)
    }
    
    # 添加图片
    if product.main_image:
        structured_data["image"] = url_for('static', filename=f'uploads/products/{product.main_image}', _external=True)
    
    # 添加价格信息
    if product.price:
        structured_data["offers"] = {
            "@type": "Offer",
            "price": str(product.price),
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock",
            "url": url_for('main.product_detail', slug=product.slug, _external=True)
        }
    
    # 添加SKU
    if product.sku:
        structured_data["sku"] = product.sku
    
    return structured_data

def get_structured_data_for_organization():
    """为组织生成结构化数据"""
    
    structured_data = {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Pecco Pet Portal",
        "description": "Light luxury pet supplies for a pawsome life",
        "url": url_for('main.index', _external=True),
        "logo": url_for('static', filename='images/logo.png', _external=True),
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "******-123-4567",
            "contactType": "customer service",
            "email": "<EMAIL>"
        },
        "sameAs": [
            "https://facebook.com/pecco",
            "https://twitter.com/pecco",
            "https://instagram.com/pecco"
        ]
    }
    
    return structured_data

def get_structured_data_for_website():
    """为网站生成结构化数据"""
    
    structured_data = {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Pecco Pet Portal",
        "description": "Light luxury pet supplies for a pawsome life",
        "url": url_for('main.index', _external=True),
        "potentialAction": {
            "@type": "SearchAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": url_for('main.search', _external=True) + "?q={search_term_string}"
            },
            "query-input": "required name=search_term_string"
        }
    }
    
    return structured_data

def optimize_meta_description(content, max_length=160):
    """优化meta描述"""
    if not content:
        return ""
    
    # 移除HTML标签
    import re
    clean_content = re.sub(r'<[^>]+>', '', content)
    
    # 移除多余空白
    clean_content = ' '.join(clean_content.split())
    
    # 截断到合适长度
    if len(clean_content) <= max_length:
        return clean_content
    
    # 在单词边界截断
    truncated = clean_content[:max_length]
    last_space = truncated.rfind(' ')
    
    if last_space > max_length * 0.8:  # 如果最后一个空格位置合理
        return truncated[:last_space] + '...'
    else:
        return truncated + '...'

def generate_canonical_url(endpoint, **kwargs):
    """生成规范URL"""
    return url_for(endpoint, _external=True, **kwargs)

def get_hreflang_links(endpoint, **kwargs):
    """生成hreflang链接"""
    links = []
    
    # 支持的语言
    languages = ['en', 'zh']
    
    for lang in languages:
        links.append({
            'hreflang': lang,
            'href': url_for(endpoint, _external=True, lang=lang, **kwargs)
        })
    
    # 添加默认语言
    links.append({
        'hreflang': 'x-default',
        'href': url_for(endpoint, _external=True, **kwargs)
    })
    
    return links
