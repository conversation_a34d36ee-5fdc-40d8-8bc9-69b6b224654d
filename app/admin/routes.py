from flask import render_template, redirect, url_for, flash, request
from app.admin import bp

@bp.route('/')
def index():
    """后台管理首页"""
    return redirect('/admin')  # 重定向到Flask-Admin

@bp.route('/dashboard')
def dashboard():
    """管理仪表板"""
    from app.models import Product, Category, Banner, Review
    
    # 统计数据
    stats = {
        'total_products': Product.query.filter_by(is_active=True).count(),
        'total_categories': Category.query.filter_by(is_active=True).count(),
        'total_banners': Banner.query.filter_by(is_active=True).count(),
        'total_reviews': Review.query.filter_by(is_active=True).count(),
        'featured_products': Product.query.filter_by(is_active=True, is_featured=True).count(),
        'new_products': Product.query.filter_by(is_active=True, is_new=True).count(),
    }
    
    # 最新产品
    latest_products = Product.query.filter_by(is_active=True)\
                                  .order_by(Product.created_at.desc())\
                                  .limit(5).all()
    
    # 最新评价
    latest_reviews = Review.query.filter_by(is_active=True)\
                                .order_by(Review.created_at.desc())\
                                .limit(5).all()
    
    return render_template('admin/dashboard.html',
                         stats=stats,
                         latest_products=latest_products,
                         latest_reviews=latest_reviews)
