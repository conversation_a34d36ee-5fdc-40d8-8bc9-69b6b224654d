import os
from flask import url_for, redirect, request, flash
from flask_admin import AdminIndexView, expose
from flask_admin.contrib.sqla import ModelView
from flask_admin.form import ImageUploadField
from flask_admin.helpers import get_url
from werkzeug.utils import secure_filename
from app.models import Category, Product, Banner, Page, Review, Setting, NewsPost
from app import db
import json

class SecureModelView(ModelView):
    """安全的模型视图基类"""
    def is_accessible(self):
        # 这里可以添加认证逻辑
        return True
    
    def inaccessible_callback(self, name, **kwargs):
        return redirect(url_for('main.index'))

class CategoryAdmin(SecureModelView):
    """分类管理"""
    column_list = ['name_en', 'name_zh', 'emoji', 'is_active', 'sort_order', 'created_at']
    column_searchable_list = ['name_en', 'name_zh']
    column_filters = ['is_active']
    column_editable_list = ['is_active', 'sort_order']
    column_labels = {
        'name_en': 'English Name',
        'name_zh': 'Chinese Name',
        'slug': 'URL Slug',
        'description_en': 'English Description',
        'description_zh': 'Chinese Description',
        'icon_image': 'Icon Image',
        'emoji': 'Emoji',
        'is_active': 'Active',
        'sort_order': 'Sort Order',
        'created_at': 'Created At',
        'updated_at': 'Updated At'
    }
    
    form_excluded_columns = ['products', 'created_at', 'updated_at']
    
    def on_model_change(self, form, model, is_created):
        if not model.slug and model.name_en:
            from slugify import slugify
            model.slug = slugify(model.name_en)

class ProductAdmin(SecureModelView):
    """产品管理"""
    column_list = ['title_en', 'category', 'vendor', 'price', 'is_featured', 'is_new', 'is_hot', 'is_active']
    column_searchable_list = ['title_en', 'title_zh', 'vendor', 'sku']
    column_filters = ['category', 'vendor', 'is_featured', 'is_new', 'is_hot', 'is_active']
    column_editable_list = ['price', 'is_featured', 'is_new', 'is_hot', 'is_active']
    column_labels = {
        'title_en': 'English Title',
        'title_zh': 'Chinese Title',
        'slug': 'URL Slug',
        'description_en': 'English Description',
        'description_zh': 'Chinese Description',
        'short_description_en': 'English Short Description',
        'short_description_zh': 'Chinese Short Description',
        'main_image': 'Main Image',
        'gallery_images': 'Gallery Images',
        'price': 'Price',
        'compare_price': 'Compare Price',
        'sku': 'SKU',
        'vendor': 'Vendor',
        'is_featured': 'Featured',
        'is_new': 'New',
        'is_hot': 'Hot',
        'is_active': 'Active',
        'category': 'Category',
        'sort_order': 'Sort Order'
    }
    
    form_excluded_columns = ['reviews', 'created_at', 'updated_at']
    form_widget_args = {
        'description_en': {'rows': 10},
        'description_zh': {'rows': 10},
        'short_description_en': {'rows': 3},
        'short_description_zh': {'rows': 3},
        'gallery_images': {'rows': 3}
    }
    
    def on_model_change(self, form, model, is_created):
        if not model.slug and model.title_en:
            from slugify import slugify
            model.slug = slugify(model.title_en)

class BannerAdmin(SecureModelView):
    """轮播图管理"""
    column_list = ['title_en', 'title_zh', 'is_active', 'sort_order', 'start_date', 'end_date']
    column_searchable_list = ['title_en', 'title_zh']
    column_filters = ['is_active']
    column_editable_list = ['is_active', 'sort_order']
    column_labels = {
        'title_en': 'English Title',
        'title_zh': 'Chinese Title',
        'subtitle_en': 'English Subtitle',
        'subtitle_zh': 'Chinese Subtitle',
        'description_en': 'English Description',
        'description_zh': 'Chinese Description',
        'image_desktop': 'Desktop Image',
        'image_mobile': 'Mobile Image',
        'link_url': 'Link URL',
        'button_text_en': 'English Button Text',
        'button_text_zh': 'Chinese Button Text',
        'is_active': 'Active',
        'sort_order': 'Sort Order',
        'start_date': 'Start Date',
        'end_date': 'End Date'
    }
    
    form_excluded_columns = ['created_at', 'updated_at']

class PageAdmin(SecureModelView):
    """页面管理"""
    column_list = ['title_en', 'title_zh', 'slug', 'is_active', 'created_at']
    column_searchable_list = ['title_en', 'title_zh', 'slug']
    column_filters = ['is_active']
    column_editable_list = ['is_active']
    column_labels = {
        'title_en': 'English Title',
        'title_zh': 'Chinese Title',
        'slug': 'URL Slug',
        'content_en': 'English Content',
        'content_zh': 'Chinese Content',
        'meta_title_en': 'English Meta Title',
        'meta_title_zh': 'Chinese Meta Title',
        'meta_description_en': 'English Meta Description',
        'meta_description_zh': 'Chinese Meta Description',
        'is_active': 'Active'
    }
    
    form_excluded_columns = ['created_at', 'updated_at']
    form_widget_args = {
        'content_en': {'rows': 15},
        'content_zh': {'rows': 15},
        'meta_description_en': {'rows': 3},
        'meta_description_zh': {'rows': 3}
    }
    
    def on_model_change(self, form, model, is_created):
        if not model.slug and model.title_en:
            from slugify import slugify
            model.slug = slugify(model.title_en)

class ReviewAdmin(SecureModelView):
    """评价管理"""
    column_list = ['customer_name', 'rating', 'product', 'is_active', 'is_featured', 'created_at']
    column_searchable_list = ['customer_name', 'title_en', 'title_zh']
    column_filters = ['rating', 'is_active', 'is_featured', 'product']
    column_editable_list = ['is_active', 'is_featured']
    column_labels = {
        'customer_name': 'Customer Name',
        'customer_avatar': 'Customer Avatar',
        'rating': 'Rating',
        'title_en': 'English Title',
        'title_zh': 'Chinese Title',
        'content_en': 'English Content',
        'content_zh': 'Chinese Content',
        'product': 'Product',
        'is_active': 'Active',
        'is_featured': 'Featured'
    }
    
    form_excluded_columns = ['created_at', 'updated_at']
    form_widget_args = {
        'content_en': {'rows': 5},
        'content_zh': {'rows': 5}
    }

class SettingAdmin(SecureModelView):
    """设置管理"""
    column_list = ['key', 'value_en', 'value_zh', 'description', 'setting_type']
    column_searchable_list = ['key', 'description']
    column_filters = ['setting_type']
    column_labels = {
        'key': 'Setting Key',
        'value_en': 'English Value',
        'value_zh': 'Chinese Value',
        'description': 'Description',
        'setting_type': 'Type'
    }
    
    form_excluded_columns = ['created_at', 'updated_at']
    form_widget_args = {
        'value_en': {'rows': 3},
        'value_zh': {'rows': 3}
    }

class NewsPostAdmin(SecureModelView):
    """新闻管理"""
    column_list = ['title_en', 'title_zh', 'is_published', 'is_featured', 'published_at', 'created_at']
    column_searchable_list = ['title_en', 'title_zh']
    column_filters = ['is_published', 'is_featured']
    column_editable_list = ['is_published', 'is_featured']
    column_labels = {
        'title_en': 'English Title',
        'title_zh': 'Chinese Title',
        'slug': 'URL Slug',
        'excerpt_en': 'English Excerpt',
        'excerpt_zh': 'Chinese Excerpt',
        'content_en': 'English Content',
        'content_zh': 'Chinese Content',
        'featured_image': 'Featured Image',
        'is_published': 'Published',
        'is_featured': 'Featured',
        'published_at': 'Published At'
    }
    
    form_excluded_columns = ['created_at', 'updated_at']
    form_widget_args = {
        'excerpt_en': {'rows': 3},
        'excerpt_zh': {'rows': 3},
        'content_en': {'rows': 15},
        'content_zh': {'rows': 15}
    }
    
    def on_model_change(self, form, model, is_created):
        if not model.slug and model.title_en:
            from slugify import slugify
            model.slug = slugify(model.title_en)

class CustomAdminIndexView(AdminIndexView):
    """自定义管理首页"""
    @expose('/')
    def index(self):
        # 统计数据
        stats = {
            'total_products': Product.query.filter_by(is_active=True).count(),
            'total_categories': Category.query.filter_by(is_active=True).count(),
            'total_banners': Banner.query.filter_by(is_active=True).count(),
            'total_reviews': Review.query.filter_by(is_active=True).count(),
        }
        
        # 最新产品
        latest_products = Product.query.filter_by(is_active=True)\
                                      .order_by(Product.created_at.desc())\
                                      .limit(5).all()
        
        return self.render('admin/index.html',
                         stats=stats,
                         latest_products=latest_products)

def setup_admin_views(admin, db):
    """设置管理视图"""
    # 设置自定义首页
    admin.index_view = CustomAdminIndexView()
    
    # 添加模型视图
    admin.add_view(CategoryAdmin(Category, db.session, name='Categories', category='Content'))
    admin.add_view(ProductAdmin(Product, db.session, name='Products', category='Content'))
    admin.add_view(BannerAdmin(Banner, db.session, name='Banners', category='Content'))
    admin.add_view(PageAdmin(Page, db.session, name='Pages', category='Content'))
    admin.add_view(ReviewAdmin(Review, db.session, name='Reviews', category='Content'))
    admin.add_view(NewsPostAdmin(NewsPost, db.session, name='News', category='Content'))
    admin.add_view(SettingAdmin(Setting, db.session, name='Settings', category='System'))
