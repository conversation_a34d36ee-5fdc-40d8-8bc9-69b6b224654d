#!/usr/bin/env python3
"""
Pecco Pet Portal - 测试运行器
运行所有测试并生成报告
"""

import unittest
import sys
import os
import coverage
from io import StringIO

def run_tests_with_coverage():
    """运行带覆盖率的测试"""
    
    # 初始化覆盖率
    cov = coverage.Coverage()
    cov.start()
    
    # 发现并运行测试
    loader = unittest.TestLoader()
    start_dir = 'tests'
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 运行测试
    stream = StringIO()
    runner = unittest.TextTestRunner(stream=stream, verbosity=2)
    result = runner.run(suite)
    
    # 停止覆盖率收集
    cov.stop()
    cov.save()
    
    # 打印测试结果
    print("🧪 Test Results:")
    print("=" * 50)
    print(stream.getvalue())
    
    # 打印覆盖率报告
    print("\n📊 Coverage Report:")
    print("=" * 50)
    cov.report()
    
    # 生成HTML覆盖率报告
    try:
        cov.html_report(directory='htmlcov')
        print("\n📄 HTML coverage report generated in 'htmlcov' directory")
    except Exception as e:
        print(f"Failed to generate HTML report: {e}")
    
    # 返回测试结果
    return result.wasSuccessful()

def run_simple_tests():
    """运行简单测试（无覆盖率）"""
    
    # 发现并运行测试
    loader = unittest.TestLoader()
    start_dir = 'tests'
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def check_test_environment():
    """检查测试环境"""
    print("🔍 Checking test environment...")
    
    # 检查必要的目录
    required_dirs = ['tests', 'app']
    for directory in required_dirs:
        if not os.path.exists(directory):
            print(f"❌ Missing directory: {directory}")
            return False
        else:
            print(f"✅ Found directory: {directory}")
    
    # 检查测试文件
    test_files = []
    if os.path.exists('tests'):
        test_files = [f for f in os.listdir('tests') if f.startswith('test_') and f.endswith('.py')]
    
    if not test_files:
        print("❌ No test files found in tests directory")
        return False
    else:
        print(f"✅ Found {len(test_files)} test files: {', '.join(test_files)}")
    
    # 检查应用模块
    try:
        from app import create_app
        print("✅ App module can be imported")
    except ImportError as e:
        print(f"❌ Cannot import app module: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🐾 Pecco Pet Portal - Test Runner")
    print("=" * 50)
    
    # 检查测试环境
    if not check_test_environment():
        print("\n❌ Test environment check failed!")
        sys.exit(1)
    
    print("\n🚀 Starting tests...")
    
    # 检查是否安装了coverage
    try:
        import coverage
        use_coverage = True
        print("📊 Coverage analysis enabled")
    except ImportError:
        use_coverage = False
        print("⚠️  Coverage package not found, running tests without coverage")
        print("   Install with: pip install coverage")
    
    # 运行测试
    if use_coverage:
        success = run_tests_with_coverage()
    else:
        success = run_simple_tests()
    
    # 输出结果
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed!")
        print("\n📋 Next steps:")
        print("1. Review any warnings or issues")
        print("2. Check coverage report (if generated)")
        print("3. Run performance tests if needed")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        print("\n📋 Next steps:")
        print("1. Review failed test output above")
        print("2. Fix the issues and run tests again")
        print("3. Check application logs for more details")
        sys.exit(1)

if __name__ == '__main__':
    main()
