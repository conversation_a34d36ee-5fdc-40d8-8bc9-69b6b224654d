/* ===== PECCO PET PORTAL STYLES ===== */

/* CSS Variables - Light Luxury Pet Theme */
:root {
    /* Primary Colors - Warm and Luxurious */
    --primary-color: #e1732c;
    --primary-dark: #c85a1a;
    --primary-light: #f4a661;
    
    /* Secondary Colors - Soft and Pet-friendly */
    --secondary-color: #051c42;
    --secondary-light: #234bbb;
    --accent-pink: #ffb6c1;
    --accent-blue: #87ceeb;
    --accent-mint: #98fb98;
    --accent-gold: #ffd700;
    
    /* Neutral Colors */
    --white: #ffffff;
    --cream: #faf8f5;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --dark-gray: #495057;
    --black: #212529;
    
    /* Typography */
    --font-family: 'Jost', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-base: 16px;
    --line-height-base: 1.6;
    
    /* Spacing */
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
    
    /* Transitions */
    --transition: all 0.3s ease;
}

/* ===== BASE STYLES ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--dark-gray);
    background-color: var(--cream);
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family);
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }

/* ===== BUTTONS ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 12px 24px;
    transition: var(--transition);
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    color: var(--white);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: var(--secondary-light);
    color: var(--white);
    transform: translateY(-2px);
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    overflow: hidden;
    background: var(--white);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.card-img-top {
    transition: var(--transition);
    height: 250px;
    object-fit: cover;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* ===== HEADER STYLES ===== */
.top-bar {
    background: var(--secondary-color);
    color: var(--white);
    padding: 8px 0;
    font-size: 14px;
}

.navbar {
    padding: 1rem 0;
    background: var(--white) !important;
    box-shadow: var(--shadow);
}

.navbar-brand {
    font-weight: 700;
    color: var(--secondary-color) !important;
}

.brand-name {
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.brand-slogan {
    font-size: 0.8rem;
    color: var(--primary-color);
    font-style: italic;
}

.nav-link {
    font-weight: 500;
    color: var(--dark-gray) !important;
    padding: 8px 16px !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background: rgba(225, 115, 44, 0.1);
}

/* ===== HERO SECTION ===== */
.hero-banner {
    position: relative;
    height: 70vh;
    min-height: 500px;
    overflow: hidden;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

.hero-slide {
    position: relative;
    height: 100%;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(5, 28, 66, 0.7), rgba(225, 115, 44, 0.3));
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--white);
    text-align: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* ===== CATEGORY SECTION ===== */
.category-card {
    text-align: center;
    padding: 2rem 1rem;
    border-radius: var(--border-radius-lg);
    background: var(--white);
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-decoration: none;
    color: inherit;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
    color: inherit;
    text-decoration: none;
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--accent-pink), var(--accent-blue));
    color: var(--white);
    box-shadow: var(--shadow);
}

.category-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.category-count {
    color: var(--gray);
    font-size: 0.9rem;
}

/* ===== PRODUCT CARDS ===== */
.product-card {
    position: relative;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 12px;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.new { background: var(--accent-mint); color: var(--dark-gray); }
.product-badge.hot { background: var(--primary-color); }
.product-badge.featured { background: var(--accent-gold); color: var(--dark-gray); }

.product-info {
    padding: 1.5rem;
}

.product-vendor {
    font-size: 0.8rem;
    color: var(--gray);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.product-description {
    font-size: 0.9rem;
    color: var(--gray);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.product-price .compare-price {
    font-size: 1rem;
    color: var(--gray);
    text-decoration: line-through;
    margin-left: 0.5rem;
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile First Approach */
@media (max-width: 576px) {
    /* Typography adjustments for small screens */
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.3rem; }

    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .hero-content {
        padding: 1rem;
    }

    /* Navigation improvements */
    .navbar-brand .brand-name {
        font-size: 1.2rem;
    }

    .navbar-brand .brand-slogan {
        font-size: 0.7rem;
    }

    /* Category cards mobile optimization */
    .category-icon {
        width: 50px;
        height: 50px;
        font-size: 1.8rem;
        margin-bottom: 0.75rem;
    }

    .category-name {
        font-size: 1rem;
    }

    .category-count {
        font-size: 0.8rem;
    }

    /* Product cards mobile optimization */
    .product-image {
        height: 200px;
    }

    .product-info {
        padding: 1rem 0.75rem;
    }

    .product-title {
        font-size: 0.95rem;
        line-height: 1.3;
    }

    .product-price {
        font-size: 1rem;
    }

    /* Button optimizations */
    .btn {
        padding: 12px 20px;
        font-size: 0.9rem;
        min-height: 44px; /* Touch target size */
    }

    .btn-lg {
        padding: 14px 24px;
        font-size: 1rem;
    }

    /* Section spacing */
    .section-padding {
        padding: 3rem 0;
    }

    .section-title h2 {
        font-size: 1.8rem;
        margin-bottom: 0.75rem;
    }

    .section-title p {
        font-size: 1rem;
    }

    /* Form improvements */
    .form-control, .form-select {
        padding: 12px 16px;
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 44px;
    }

    /* Modal improvements */
    .modal-dialog {
        margin: 1rem;
    }

    /* Carousel improvements */
    .carousel-control-prev,
    .carousel-control-next {
        width: 44px;
    }

    /* Footer adjustments */
    .footer .row > div {
        margin-bottom: 2rem;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    /* Tablet specific adjustments */
    .product-image {
        height: 220px;
    }

    .section-padding {
        padding: 4rem 0;
    }
}

/* Large mobile devices */
@media (min-width: 576px) and (max-width: 767px) {
    .hero-title {
        font-size: 2.2rem;
    }

    .category-icon {
        width: 55px;
        height: 55px;
        font-size: 1.9rem;
    }

    .product-image {
        height: 210px;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .card:hover {
        transform: none;
        box-shadow: var(--shadow);
    }

    .product-card:hover {
        transform: none;
    }

    .product-card:hover .product-image img {
        transform: none;
    }

    .category-card:hover {
        transform: none;
        background-color: var(--white);
    }

    /* Improve touch targets */
    .nav-link {
        padding: 12px 16px !important;
    }

    .pagination .page-link {
        padding: 12px 16px;
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Optimize for retina displays */
    .product-image img,
    .category-icon img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== UTILITY CLASSES ===== */
.bg-cream { background-color: var(--cream) !important; }
.bg-light-gray { background-color: var(--light-gray) !important; }
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-padding {
    padding: 5rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--gray);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== MOBILE-SPECIFIC STYLES ===== */

/* Touch feedback */
.touch-active {
    transform: scale(0.98);
    opacity: 0.8;
    transition: all 0.1s ease;
}

/* Mobile navigation improvements */
.navbar-toggler {
    border: none;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    background: rgba(225, 115, 44, 0.1);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(225, 115, 44, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28225, 115, 44, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Mobile dropdown improvements */
@media (max-width: 991px) {
    .navbar-nav .dropdown-menu {
        border: none;
        box-shadow: none;
        background: rgba(248, 249, 250, 0.95);
        margin-left: 1rem;
        margin-top: 0.5rem;
        border-radius: var(--border-radius);
    }

    .navbar-nav .dropdown-item {
        padding: 12px 16px;
        font-size: 0.95rem;
    }
}

/* Mobile form improvements */
.mobile-form-group {
    margin-bottom: 1.5rem;
}

.mobile-form-group .form-label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--secondary-color);
}

/* Mobile card improvements */
.mobile-card-stack .card {
    margin-bottom: 1rem;
    border-radius: var(--border-radius-lg);
}

/* Mobile pagination */
@media (max-width: 576px) {
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination .page-item {
        margin: 2px;
    }

    .pagination .page-link {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
}

/* Mobile modal improvements */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100vw - 1rem);
    }

    .modal-content {
        border-radius: var(--border-radius-lg);
    }

    .modal-header {
        padding: 1rem 1.25rem 0.5rem;
    }

    .modal-body {
        padding: 1rem 1.25rem;
    }

    .modal-footer {
        padding: 0.5rem 1.25rem 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-footer .btn {
        width: 100%;
        margin: 0;
    }
}

/* Mobile table improvements */
@media (max-width: 768px) {
    .table-responsive {
        border: none;
    }

    .mobile-table-card {
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
        padding: 1rem;
        background: var(--white);
    }

    .mobile-table-card .table-label {
        font-weight: 600;
        color: var(--secondary-color);
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .mobile-table-card .table-value {
        margin-bottom: 0.75rem;
    }
}

/* Mobile breadcrumb */
@media (max-width: 576px) {
    .breadcrumb {
        font-size: 0.85rem;
        padding: 0.5rem 0;
        margin-bottom: 1rem;
        background: none;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        font-size: 1.1rem;
    }
}

/* Mobile search improvements */
.mobile-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: none;
}

.mobile-search-container {
    position: absolute;
    top: 2rem;
    left: 1rem;
    right: 1rem;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-hover);
}

/* Mobile sticky elements */
.mobile-sticky-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--white);
    border-top: 1px solid var(--border-color);
    padding: 1rem;
    z-index: 100;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

/* Mobile floating action button */
.mobile-fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    box-shadow: var(--shadow-hover);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    z-index: 99;
    transition: var(--transition);
}

.mobile-fab:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

/* Mobile swipe indicators */
.mobile-swipe-indicator {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 10;
}

/* Mobile loading states */
.mobile-loading {
    position: relative;
    overflow: hidden;
}

.mobile-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: mobile-shimmer 1.5s infinite;
}

@keyframes mobile-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
