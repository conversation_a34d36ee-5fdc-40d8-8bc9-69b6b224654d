/**
 * Pecco Pet Portal - Main JavaScript
 * Light luxury pet supplies website
 */

(function($) {
    'use strict';

    // Global variables
    let isLoading = false;

    // Document ready
    $(document).ready(function() {
        initializeApp();
    });

    /**
     * Initialize the application
     */
    function initializeApp() {
        initHeroBanner();
        initLazyLoading();
        initSmoothScrolling();
        initFormValidation();
        initTooltips();
        initAnimations();
        initLanguageSwitcher();
        initMobileMenu();
        
        console.log('🐾 Pecco Pet Portal initialized successfully!');
    }

    /**
     * Initialize hero banner carousel
     */
    function initHeroBanner() {
        const heroCarousel = $('#heroCarousel');
        if (heroCarousel.length) {
            heroCarousel.carousel({
                interval: 5000,
                pause: 'hover',
                wrap: true
            });

            // Add fade effect
            heroCarousel.on('slide.bs.carousel', function(e) {
                const $animatingElems = $(e.relatedTarget).find('.hero-content');
                $animatingElems.addClass('fade-in-up');
            });
        }
    }

    /**
     * Initialize lazy loading for images
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('fade-in');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Initialize smooth scrolling
     */
    function initSmoothScrolling() {
        $('a[href*="#"]:not([href="#"])').click(function() {
            if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') 
                && location.hostname == this.hostname) {
                let target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                    return false;
                }
            }
        });
    }

    /**
     * Initialize form validation
     */
    function initFormValidation() {
        // Bootstrap form validation
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // Newsletter form
        $('#newsletter-form').on('submit', function(e) {
            e.preventDefault();
            const email = $(this).find('input[type="email"]').val();
            if (validateEmail(email)) {
                showNotification('Thank you for subscribing! 💌', 'success');
                $(this)[0].reset();
            } else {
                showNotification('Please enter a valid email address.', 'error');
            }
        });
    }

    /**
     * Initialize tooltips
     */
    function initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    /**
     * Initialize animations
     */
    function initAnimations() {
        // Animate elements on scroll
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in-up');
                        animationObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1
            });

            document.querySelectorAll('.animate-on-scroll').forEach(el => {
                animationObserver.observe(el);
            });
        }

        // Product card hover effects
        $('.product-card').hover(
            function() {
                $(this).find('.product-image img').addClass('scale-hover');
            },
            function() {
                $(this).find('.product-image img').removeClass('scale-hover');
            }
        );
    }

    /**
     * Initialize language switcher
     */
    function initLanguageSwitcher() {
        $('.language-switcher a').on('click', function(e) {
            e.preventDefault();
            const lang = $(this).data('lang');
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('lang', lang);
            window.location.href = currentUrl.toString();
        });
    }

    /**
     * Initialize mobile menu
     */
    function initMobileMenu() {
        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.navbar').length) {
                $('.navbar-collapse').collapse('hide');
            }
        });

        // Smooth close animation for mobile menu
        $('.navbar-nav .nav-link').on('click', function() {
            if (window.innerWidth < 992) {
                $('.navbar-collapse').collapse('hide');
            }
        });

        // Mobile menu toggle improvements
        $('.navbar-toggler').on('click', function() {
            const isExpanded = $(this).attr('aria-expanded') === 'true';
            $(this).attr('aria-expanded', !isExpanded);
        });

        // Prevent menu close when clicking on dropdown
        $('.navbar-nav .dropdown-menu').on('click', function(e) {
            e.stopPropagation();
        });

        // Mobile-specific touch improvements
        if (isMobileDevice()) {
            initMobileTouchOptimizations();
        }
    }

    /**
     * Initialize mobile touch optimizations
     */
    function initMobileTouchOptimizations() {
        // Improve touch scrolling
        $('body').css({
            '-webkit-overflow-scrolling': 'touch',
            'overflow-scrolling': 'touch'
        });

        // Add touch feedback for buttons
        $('.btn, .card, .product-card').on('touchstart', function() {
            $(this).addClass('touch-active');
        }).on('touchend touchcancel', function() {
            $(this).removeClass('touch-active');
        });

        // Optimize carousel for touch
        if ($('.carousel').length) {
            $('.carousel').on('touchstart', function(e) {
                const touch = e.originalEvent.touches[0];
                $(this).data('touchStartX', touch.clientX);
            }).on('touchmove', function(e) {
                e.preventDefault(); // Prevent scrolling while swiping
            }).on('touchend', function(e) {
                const touch = e.originalEvent.changedTouches[0];
                const startX = $(this).data('touchStartX');
                const endX = touch.clientX;
                const diff = startX - endX;

                if (Math.abs(diff) > 50) { // Minimum swipe distance
                    if (diff > 0) {
                        $(this).carousel('next');
                    } else {
                        $(this).carousel('prev');
                    }
                }
            });
        }

        // Improve form interactions on mobile
        $('input, textarea, select').on('focus', function() {
            // Scroll to input on focus (iOS fix)
            setTimeout(() => {
                this.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 300);
        });

        // Add pull-to-refresh hint (visual only)
        if (window.location.pathname === '/') {
            addPullToRefreshHint();
        }
    }

    /**
     * Check if device is mobile
     */
    function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }

    /**
     * Add pull-to-refresh visual hint
     */
    function addPullToRefreshHint() {
        let startY = 0;
        let currentY = 0;
        let pullDistance = 0;
        const maxPull = 100;

        $(document).on('touchstart', function(e) {
            if (window.pageYOffset === 0) {
                startY = e.originalEvent.touches[0].clientY;
            }
        });

        $(document).on('touchmove', function(e) {
            if (window.pageYOffset === 0 && startY) {
                currentY = e.originalEvent.touches[0].clientY;
                pullDistance = Math.min(currentY - startY, maxPull);

                if (pullDistance > 0) {
                    $('body').css('transform', `translateY(${pullDistance * 0.5}px)`);

                    if (pullDistance > 60) {
                        showPullToRefreshIndicator();
                    }
                }
            }
        });

        $(document).on('touchend', function() {
            if (pullDistance > 60) {
                window.location.reload();
            } else {
                $('body').css('transform', '');
                hidePullToRefreshIndicator();
            }
            startY = 0;
            pullDistance = 0;
        });
    }

    /**
     * Show pull-to-refresh indicator
     */
    function showPullToRefreshIndicator() {
        if (!$('#pull-refresh-indicator').length) {
            $('body').prepend(`
                <div id="pull-refresh-indicator" style="
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: var(--primary-color);
                    color: white;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 14px;
                    z-index: 9999;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                ">
                    <i class="fas fa-sync-alt"></i> Release to refresh
                </div>
            `);
        }
        $('#pull-refresh-indicator').css('opacity', '1');
    }

    /**
     * Hide pull-to-refresh indicator
     */
    function hidePullToRefreshIndicator() {
        $('#pull-refresh-indicator').css('opacity', '0');
    }

    /**
     * Show loading overlay
     */
    function showLoading() {
        if (!isLoading) {
            isLoading = true;
            $('#loading-overlay').removeClass('d-none').fadeIn(300);
        }
    }

    /**
     * Hide loading overlay
     */
    function hideLoading() {
        if (isLoading) {
            isLoading = false;
            $('#loading-overlay').fadeOut(300, function() {
                $(this).addClass('d-none');
            });
        }
    }

    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
        const notification = $(`
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('body').append(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.alert('close');
        }, 5000);
    }

    /**
     * Validate email address
     */
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    /**
     * Format price
     */
    function formatPrice(price, currency = '$') {
        return `${currency}${parseFloat(price).toFixed(2)}`;
    }

    /**
     * Debounce function
     */
    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    /**
     * Handle window resize
     */
    $(window).on('resize', debounce(function() {
        // Handle responsive adjustments
        if (window.innerWidth >= 992) {
            $('.navbar-collapse').removeClass('show');
        }
    }, 250));

    /**
     * Handle scroll events
     */
    $(window).on('scroll', debounce(function() {
        const scrollTop = $(window).scrollTop();
        
        // Add shadow to navbar on scroll
        if (scrollTop > 100) {
            $('.navbar').addClass('scrolled');
        } else {
            $('.navbar').removeClass('scrolled');
        }
    }, 100));

    // Expose global functions
    window.PeccoApp = {
        showLoading,
        hideLoading,
        showNotification,
        validateEmail,
        formatPrice
    };

})(jQuery);

// Additional CSS for dynamic effects
const additionalCSS = `
    .scale-hover {
        transform: scale(1.1) !important;
    }
    
    .navbar.scrolled {
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1) !important;
    }
    
    .fade-in {
        opacity: 1 !important;
        transition: opacity 0.6s ease-in-out;
    }
    
    img.lazy {
        opacity: 0;
    }
`;

// Inject additional CSS
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);
