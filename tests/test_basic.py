"""
Pecco Pet Portal - 基础测试
"""

import unittest
import os
import tempfile
from app import create_app, db
from app.models import Category, Product, Banner, Setting

class BasicTestCase(unittest.TestCase):
    """基础测试用例"""
    
    def setUp(self):
        """测试前设置"""
        self.db_fd, self.db_path = tempfile.mkstemp()
        
        # 创建测试应用
        self.app = create_app('testing')
        self.app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{self.db_path}'
        self.app.config['TESTING'] = True
        self.app.config['WTF_CSRF_ENABLED'] = False
        
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        self.client = self.app.test_client()
        
        # 创建数据库表
        db.create_all()
        
        # 创建测试数据
        self.create_test_data()
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        os.close(self.db_fd)
        os.unlink(self.db_path)
    
    def create_test_data(self):
        """创建测试数据"""
        # 创建测试分类
        category = Category(
            name_en='Test Dogs',
            name_zh='测试狗狗',
            slug='test-dogs',
            emoji='🐶'
        )
        db.session.add(category)
        
        # 创建测试产品
        product = Product(
            title_en='Test Dog Bed',
            title_zh='测试狗床',
            slug='test-dog-bed',
            description_en='A comfortable bed for dogs',
            description_zh='舒适的狗床',
            price=99.99,
            category=category,
            is_active=True
        )
        db.session.add(product)
        
        # 创建测试横幅
        banner = Banner(
            title_en='Test Banner',
            title_zh='测试横幅',
            image_desktop='test-banner.jpg',
            is_active=True
        )
        db.session.add(banner)
        
        # 创建测试设置
        setting = Setting(
            key='test_setting',
            value_en='Test Value',
            value_zh='测试值'
        )
        db.session.add(setting)
        
        db.session.commit()
    
    def test_app_exists(self):
        """测试应用是否存在"""
        self.assertIsNotNone(self.app)
    
    def test_app_is_testing(self):
        """测试应用是否在测试模式"""
        self.assertTrue(self.app.config['TESTING'])
    
    def test_homepage(self):
        """测试首页"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Pecco', response.data)
    
    def test_products_page(self):
        """测试产品页面"""
        response = self.client.get('/products')
        self.assertEqual(response.status_code, 200)
    
    def test_product_detail(self):
        """测试产品详情页"""
        response = self.client.get('/product/test-dog-bed')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Test Dog Bed', response.data)
    
    def test_category_page(self):
        """测试分类页面"""
        response = self.client.get('/category/test-dogs')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Test Dogs', response.data)
    
    def test_contact_page(self):
        """测试联系页面"""
        response = self.client.get('/contact')
        self.assertEqual(response.status_code, 200)
    
    def test_sitemap(self):
        """测试sitemap"""
        response = self.client.get('/sitemap.xml')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content_type, 'application/xml; charset=utf-8')
    
    def test_robots(self):
        """测试robots.txt"""
        response = self.client.get('/robots.txt')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content_type, 'text/plain; charset=utf-8')
    
    def test_manifest(self):
        """测试PWA manifest"""
        response = self.client.get('/manifest.json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content_type, 'application/json')
    
    def test_language_switching(self):
        """测试语言切换"""
        # 测试英文
        response = self.client.get('/?lang=en')
        self.assertEqual(response.status_code, 200)
        
        # 测试中文
        response = self.client.get('/?lang=zh')
        self.assertEqual(response.status_code, 200)
    
    def test_api_categories(self):
        """测试分类API"""
        response = self.client.get('/api/categories')
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertIsInstance(data, list)
        self.assertGreater(len(data), 0)
    
    def test_api_products(self):
        """测试产品API"""
        response = self.client.get('/api/products')
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertIn('products', data)
        self.assertIn('pagination', data)
    
    def test_search(self):
        """测试搜索功能"""
        response = self.client.get('/search?q=dog')
        self.assertEqual(response.status_code, 200)
    
    def test_404_error(self):
        """测试404错误"""
        response = self.client.get('/nonexistent-page')
        self.assertEqual(response.status_code, 404)

class ModelTestCase(unittest.TestCase):
    """模型测试用例"""
    
    def setUp(self):
        """测试前设置"""
        self.db_fd, self.db_path = tempfile.mkstemp()
        
        self.app = create_app('testing')
        self.app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{self.db_path}'
        
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        db.create_all()
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        os.close(self.db_fd)
        os.unlink(self.db_path)
    
    def test_category_model(self):
        """测试分类模型"""
        category = Category(
            name_en='Test Category',
            name_zh='测试分类',
            emoji='🐾'
        )
        db.session.add(category)
        db.session.commit()
        
        self.assertIsNotNone(category.id)
        self.assertEqual(category.name_en, 'Test Category')
        self.assertIsNotNone(category.slug)
    
    def test_product_model(self):
        """测试产品模型"""
        category = Category(name_en='Test Category', name_zh='测试分类')
        db.session.add(category)
        db.session.flush()
        
        product = Product(
            title_en='Test Product',
            title_zh='测试产品',
            price=99.99,
            category_id=category.id
        )
        db.session.add(product)
        db.session.commit()
        
        self.assertIsNotNone(product.id)
        self.assertEqual(product.title_en, 'Test Product')
        self.assertEqual(product.price, 99.99)
        self.assertIsNotNone(product.slug)
    
    def test_banner_model(self):
        """测试横幅模型"""
        banner = Banner(
            title_en='Test Banner',
            title_zh='测试横幅',
            image_desktop='test.jpg'
        )
        db.session.add(banner)
        db.session.commit()
        
        self.assertIsNotNone(banner.id)
        self.assertEqual(banner.title_en, 'Test Banner')
    
    def test_setting_model(self):
        """测试设置模型"""
        setting = Setting(
            key='test_key',
            value_en='Test Value',
            value_zh='测试值'
        )
        db.session.add(setting)
        db.session.commit()
        
        self.assertIsNotNone(setting.id)
        self.assertEqual(setting.key, 'test_key')

if __name__ == '__main__':
    unittest.main()
