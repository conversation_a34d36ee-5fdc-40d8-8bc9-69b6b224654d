# 🐾 Pecco Pet Portal

**轻奢宠物用品官网门户网站**

一个现代化的宠物用品展示网站，采用Flask框架开发，具有完整的后台管理系统和多语言支持。

## ✨ 特性

### 🎨 前端特性
- **现代轻奢设计** - 温馨宠物主题 + 简约官网美学
- **响应式布局** - 完美适配PC、平板、手机
- **多语言支持** - 中文/英文切换
- **优雅动画** - 平滑过渡和悬停效果
- **SEO友好** - 优化的URL结构和meta标签

### 🛠️ 功能模块
- **轮播横幅** - 可配置的产品展示轮播图
- **宠物分类** - 按宠物类型展示产品（🐶🐱🐦🐠等）
- **产品展示** - 完整的产品管理和展示系统
- **客户评价** - 用户反馈和评分系统
- **新闻资讯** - 宠物护理技巧和产品更新
- **联系我们** - 完整的联系表单和FAQ

### 🔧 后台管理
- **内容管理** - 轮播图、产品、分类、页面管理
- **多语言管理** - 中英文内容编辑
- **用户友好** - 简单易用的管理界面
- **数据统计** - 产品、分类、评价等统计信息

## 🚀 快速开始

### 环境要求
- Python 3.8+
- pip
- SQLite (默认) 或 PostgreSQL

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd pecco-pet-portal
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和其他设置
```

5. **初始化项目**
```bash
python run.py
```

6. **启动应用**
```bash
python app.py
```

7. **访问网站**
- 前台网站: http://localhost:5000
- 后台管理: http://localhost:5000/admin

## 📁 项目结构

```
pecco-pet-portal/
├── app/                    # 应用主目录
│   ├── __init__.py        # 应用工厂
│   ├── models.py          # 数据模型
│   ├── utils.py           # 工具函数
│   ├── main/              # 前台蓝图
│   │   ├── __init__.py
│   │   └── routes.py      # 前台路由
│   ├── admin/             # 后台蓝图
│   │   ├── __init__.py
│   │   ├── routes.py      # 后台路由
│   │   └── views.py       # 管理视图
│   ├── api/               # API蓝图
│   │   ├── __init__.py
│   │   └── routes.py      # API路由
│   ├── templates/         # 模板文件
│   │   ├── base.html      # 基础模板
│   │   ├── includes/      # 组件模板
│   │   ├── main/          # 前台模板
│   │   └── admin/         # 后台模板
│   └── translations/      # 多语言文件
├── static/                # 静态文件
│   ├── css/              # 样式文件
│   ├── js/               # JavaScript文件
│   ├── images/           # 图片文件
│   └── uploads/          # 上传文件
├── migrations/           # 数据库迁移
├── config.py            # 配置文件
├── app.py              # 应用入口
├── run.py              # 初始化脚本
├── init_data.py        # 示例数据
├── requirements.txt    # 依赖列表
└── README.md          # 项目说明
```

## 🎨 设计风格

### 色彩方案
- **主色调**: 温暖橙色 (#e1732c)
- **辅助色**: 深蓝色 (#051c42)
- **背景色**: 温馨米白 (#faf8f5)
- **强调色**: 柔和粉色、浅蓝色、薄荷绿

### 设计元素
- **圆角设计** - 柔和的边角处理
- **卡片布局** - 现代化的内容组织
- **Emoji表情** - 增加亲和力 🐶🐱✨🐾💝
- **渐变效果** - 优雅的视觉层次
- **阴影效果** - 立体感和深度

## 🔧 配置说明

### 环境变量 (.env)
```bash
# Flask配置
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key

# 数据库配置
DATABASE_URL=sqlite:///pecco_pet_portal.db

# 上传配置
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216

# 语言配置
LANGUAGES=en,zh
DEFAULT_LANGUAGE=en

# 联系信息
ADMIN_EMAIL=<EMAIL>
CONTACT_EMAIL=<EMAIL>
CONTACT_PHONE=******-123-4567
```

### 数据库配置
默认使用SQLite，生产环境建议使用PostgreSQL：
```bash
# PostgreSQL示例
DATABASE_URL=postgresql://username:password@localhost/pecco_pet_portal
```

## 📱 移动端适配

网站完全响应式设计，在各种设备上都有良好的用户体验：

- **手机端** (< 768px) - 单列布局，触摸友好
- **平板端** (768px - 1024px) - 两列布局
- **桌面端** (> 1024px) - 多列布局，完整功能

## 🌐 多语言支持

### 支持语言
- 🇺🇸 English (英语)
- 🇨🇳 中文 (简体中文)

### 添加新语言
1. 在 `config.py` 中添加语言代码
2. 创建翻译文件: `app/translations/{lang}/LC_MESSAGES/messages.po`
3. 翻译所有文本字符串
4. 编译翻译文件: `pybabel compile -d app/translations`

## 🛡️ 安全特性

- **输入验证** - 所有用户输入都经过验证
- **文件上传安全** - 限制文件类型和大小
- **SQL注入防护** - 使用ORM防止SQL注入
- **XSS防护** - 模板自动转义
- **CSRF保护** - 表单CSRF令牌

## 🚀 部署指南

### 生产环境部署

1. **设置环境变量**
```bash
export FLASK_ENV=production
export DATABASE_URL=postgresql://...
```

2. **安装生产依赖**
```bash
pip install gunicorn psycopg2-binary
```

3. **运行应用**
```bash
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

### Docker部署
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🐾 关于Pecco

Pecco Pet Portal 致力于为宠物主人提供最优质的产品和服务。我们相信每只宠物都应该得到最好的关爱和呵护。

**Happy Pets, Happy Owner! 🐾**

---

如有问题或建议，请联系我们：
- 📧 Email: <EMAIL>
- 📞 Phone: ******-123-4567
- 🌐 Website: https://pecco.com
